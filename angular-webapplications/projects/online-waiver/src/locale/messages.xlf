<?xml version="1.0" encoding="UTF-8" ?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2">
  <file source-language="en-US" datatype="plaintext" original="ng2.template">
    <body>
      <trans-unit id="2471041631220366258" datatype="html">
        <source>404</source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-app-core/src/lib/components/page-not-found/page-not-found.component.html</context>
          <context context-type="linenumber">4,5</context>
        </context-group>
        <note priority="1" from="description">page-not-found.title</note>
      </trans-unit>
      <trans-unit id="4922622500015244726" datatype="html">
        <source>Something&apos;s missing.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-app-core/src/lib/components/page-not-found/page-not-found.component.html</context>
          <context context-type="linenumber">5,6</context>
        </context-group>
        <note priority="1" from="description">page-not-found.sub-title</note>
      </trans-unit>
      <trans-unit id="9090694792823919768" datatype="html">
        <source>Sorry, we can&apos;t find that page. You&apos;ll find lots to explore on the home page. </source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-app-core/src/lib/components/page-not-found/page-not-found.component.html</context>
          <context context-type="linenumber">6,7</context>
        </context-group>
        <note priority="1" from="description">page-not-found.description</note>
      </trans-unit>
      <trans-unit id="2409871300344369828" datatype="html">
        <source>Back to Homepage</source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-app-core/src/lib/components/page-not-found/page-not-found.component.html</context>
          <context context-type="linenumber">7,9</context>
        </context-group>
        <note priority="1" from="description">page-not-found.link</note>
      </trans-unit>
      <trans-unit id="form-validation-message.email.required" datatype="html">
        <source>Email address is required</source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-app-core/src/lib/constants/form-validation-message.ts</context>
          <context context-type="linenumber">9</context>
        </context-group>
        <note priority="1" from="description">form-validation-message.email.required</note>
      </trans-unit>
      <trans-unit id="form-validation-message.email.email" datatype="html">
        <source>Please enter a valid email address</source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-app-core/src/lib/constants/form-validation-message.ts</context>
          <context context-type="linenumber">10</context>
        </context-group>
        <note priority="1" from="description">form-validation-message.email.email</note>
      </trans-unit>
      <trans-unit id="form-validation-message.password.required" datatype="html">
        <source>Password is required</source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-app-core/src/lib/constants/form-validation-message.ts</context>
          <context context-type="linenumber">13</context>
        </context-group>
        <note priority="1" from="description">form-validation-message.password.required</note>
      </trans-unit>
      <trans-unit id="form-validation-message.password.pattern" datatype="html">
        <source>At least 8 characters, 1 uppercase letter, 1 lowercase letter, 1 number and 1 special character</source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-app-core/src/lib/constants/form-validation-message.ts</context>
          <context context-type="linenumber">14</context>
        </context-group>
        <note priority="1" from="description">form-validation-message.password.pattern</note>
      </trans-unit>
      <trans-unit id="form-validation-message.confirmPassword.required" datatype="html">
        <source>Please confirm your password</source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-app-core/src/lib/constants/form-validation-message.ts</context>
          <context context-type="linenumber">17</context>
        </context-group>
        <note priority="1" from="description">form-validation-message.confirmPassword.required</note>
      </trans-unit>
      <trans-unit id="form-validation-message.confirmPassword.passwordMismatch" datatype="html">
        <source>Passwords do not match</source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-app-core/src/lib/constants/form-validation-message.ts</context>
          <context context-type="linenumber">18</context>
        </context-group>
        <note priority="1" from="description">form-validation-message.confirmPassword.passwordMismatch</note>
      </trans-unit>
      <trans-unit id="form-validation-message.dateOfBirth.belowMinorAge" datatype="html">
        <source>Age is below the minor age limit.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-app-core/src/lib/constants/form-validation-message.ts</context>
          <context context-type="linenumber">21</context>
        </context-group>
        <note priority="1" from="description">form-validation-message.dateOfBirth.belowMinorAge</note>
      </trans-unit>
      <trans-unit id="form-validation-message.dateOfBirth.aboveMinorAge" datatype="html">
        <source>Age is above the minor age limit.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-app-core/src/lib/constants/form-validation-message.ts</context>
          <context context-type="linenumber">22</context>
        </context-group>
        <note priority="1" from="description">form-validation-message.dateOfBirth.aboveMinorAge</note>
      </trans-unit>
      <trans-unit id="form-validation-message.invalidFormat" datatype="html">
        <source>Invalid format</source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-app-core/src/lib/constants/form-validation-message.ts</context>
          <context context-type="linenumber">26</context>
        </context-group>
        <note priority="1" from="description">form-validation-message.invalidFormat</note>
      </trans-unit>
      <trans-unit id="form-validation-message.passwordReset.success" datatype="html">
        <source>Your Password has been successfully reset.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-app-core/src/lib/constants/form-validation-message.ts</context>
          <context context-type="linenumber">30</context>
        </context-group>
        <note priority="1" from="description">form-validation-message.passwordReset.success</note>
      </trans-unit>
      <trans-unit id="form-validation-message.passwordReset.failure" datatype="html">
        <source>Password reset failed. Please try again.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-app-core/src/lib/constants/form-validation-message.ts</context>
          <context context-type="linenumber">31</context>
        </context-group>
        <note priority="1" from="description">form-validation-message.passwordReset.failure</note>
      </trans-unit>
      <trans-unit id="form-validation-message.passwordReset.invalidToken" datatype="html">
        <source>Invalid reset token</source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-app-core/src/lib/constants/form-validation-message.ts</context>
          <context context-type="linenumber">32</context>
        </context-group>
        <note priority="1" from="description">form-validation-message.passwordReset.invalidToken</note>
      </trans-unit>
      <trans-unit id="form-validation-message.passwordReset.invalidParameters" datatype="html">
        <source>Invalid parameters</source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-app-core/src/lib/constants/form-validation-message.ts</context>
          <context context-type="linenumber">33</context>
        </context-group>
        <note priority="1" from="description">form-validation-message.passwordReset.invalidParameters</note>
      </trans-unit>
      <trans-unit id="form-validation-message.passwordReset.noCardData" datatype="html">
        <source>No card data received</source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-app-core/src/lib/constants/form-validation-message.ts</context>
          <context context-type="linenumber">34</context>
        </context-group>
        <note priority="1" from="description">form-validation-message.passwordReset.noCardData</note>
      </trans-unit>
      <trans-unit id="continueToLogin" datatype="html">
        <source>Continue to Login</source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-app-core/src/lib/constants/navigation-constants.ts</context>
          <context context-type="linenumber">9</context>
        </context-group>
        <note priority="1" from="description">navigation-constants.continueToLogin</note>
      </trans-unit>
      <trans-unit id="retryPasswordReset" datatype="html">
        <source>Retry Password Reset</source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-app-core/src/lib/constants/navigation-constants.ts</context>
          <context context-type="linenumber">10</context>
        </context-group>
        <note priority="1" from="description">navigation-constants.retryPasswordReset</note>
      </trans-unit>
      <trans-unit id="backToLogin" datatype="html">
        <source>Back to Login</source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-app-core/src/lib/constants/navigation-constants.ts</context>
          <context context-type="linenumber">11</context>
        </context-group>
        <note priority="1" from="description">navigation-constants.backToLogin</note>
      </trans-unit>
      <trans-unit id="8264044238565501678" datatype="html">
        <source>Forgot password</source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-auth/src/lib/components/forgot-password/forgot-password.component.html</context>
          <context context-type="linenumber">7,10</context>
        </context-group>
        <note priority="1" from="description">forgot-password.title</note>
      </trans-unit>
      <trans-unit id="8354930185022857766" datatype="html">
        <source>Identify yourself</source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-auth/src/lib/components/forgot-password/forgot-password.component.html</context>
          <context context-type="linenumber">13,14</context>
        </context-group>
        <note priority="1" from="description">forgot-password.identify-yourself</note>
      </trans-unit>
      <trans-unit id="592170666290650628" datatype="html">
        <source> Please enter the registered email address to receive link to reset </source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-auth/src/lib/components/forgot-password/forgot-password.component.html</context>
          <context context-type="linenumber">15,17</context>
        </context-group>
        <note priority="1" from="description">forgot-password.please-enter-email</note>
      </trans-unit>
      <trans-unit id="935187492052582731" datatype="html">
        <source>Submit</source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-auth/src/lib/components/forgot-password/forgot-password.component.html</context>
          <context context-type="linenumber">24,26</context>
        </context-group>
        <note priority="1" from="description">forgot-password.submit</note>
      </trans-unit>
      <trans-unit id="7708270344948043036" datatype="html">
        <source> <x id="INTERPOLATION" equiv-text="{{errorMessage}}"/> </source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-auth/src/lib/components/forgot-password/forgot-password.component.html</context>
          <context context-type="linenumber">30,32</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-auth/src/lib/components/login-form/login-form.component.html</context>
          <context context-type="linenumber">25,26</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-auth/src/lib/components/status-result/status-result.component.html</context>
          <context context-type="linenumber">19,22</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/how-to-sign-waiver/how-to-sign-waiver.component.html</context>
          <context context-type="linenumber">6,8</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/participant-item/participant-item.component.html</context>
          <context context-type="linenumber">6,7</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/participant-item/participant-item.component.html</context>
          <context context-type="linenumber">9,10</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/participant-section/participant-section.component.html</context>
          <context context-type="linenumber">7,8</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/registration-form-waiver/registration-form-waiver.component.html</context>
          <context context-type="linenumber">36,39</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/sign-waiver/sign-waiver.component.html</context>
          <context context-type="linenumber">14,15</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/signed-waiver-confirmation/signed-waiver-confirmation.component.html</context>
          <context context-type="linenumber">19,20</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/site-selection/site-selection.component.html</context>
          <context context-type="linenumber">40,41</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/site-selection/site-selection.component.html</context>
          <context context-type="linenumber">43,44</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/terms-and-conditions/terms-and-conditions.component.html</context>
          <context context-type="linenumber">18,19</context>
        </context-group>
        <note priority="1" from="description">forgot-password.error-message</note>
      </trans-unit>
      <trans-unit id="6439705746328806221" datatype="html">
        <source> An email has been sent to <x id="START_TAG_STRONG" ctype="x-strong" equiv-text="&lt;strong&gt;"/><x id="INTERPOLATION" equiv-text="{{ resetPasswordForm.value.email  }}"/><x id="CLOSE_TAG_STRONG" ctype="x-strong" equiv-text="&lt;/strong&gt;"/></source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-auth/src/lib/components/forgot-password/forgot-password.component.html</context>
          <context context-type="linenumber">34,37</context>
        </context-group>
        <note priority="1" from="description">forgot-password.email-sent-to</note>
      </trans-unit>
      <trans-unit id="4045784700413635436" datatype="html">
        <source> Add the below details to get an account. We don&apos;t share or sell your data. </source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-auth/src/lib/components/login-form/login-form.component.html</context>
          <context context-type="linenumber">3,7</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-auth/src/lib/components/registration-form-base/registration-form-base.component.html</context>
          <context context-type="linenumber">3,7</context>
        </context-group>
        <note priority="1" from="description">login-form.add-details</note>
      </trans-unit>
      <trans-unit id="2727496946419791472" datatype="html">
        <source>Forgot password?</source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-auth/src/lib/components/login-form/login-form.component.html</context>
          <context context-type="linenumber">21,23</context>
        </context-group>
        <note priority="1" from="description">login-form.forgot-password</note>
      </trans-unit>
      <trans-unit id="187187500641108332" datatype="html">
        <source><x id="INTERPOLATION" equiv-text="{{ loading() ? &quot;Logging in...&quot; : &quot;Login&quot; }}"/></source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-auth/src/lib/components/login-form/login-form.component.html</context>
          <context context-type="linenumber">49,50</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-auth/src/lib/components/registration-form-base/registration-form-base.component.html</context>
          <context context-type="linenumber">95,96</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-ui-kit/src/lib/components/form-controls/checkbox/checkbox.component.html</context>
          <context context-type="linenumber">28,30</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-ui-kit/src/lib/components/form-controls/date-picker/date-picker.component.html</context>
          <context context-type="linenumber">40,42</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-ui-kit/src/lib/components/form-controls/phone-input/phone-input.component.html</context>
          <context context-type="linenumber">70,72</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-ui-kit/src/lib/components/form-controls/select/select.component.html</context>
          <context context-type="linenumber">53,54</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-ui-kit/src/lib/components/form-controls/text-input/text-input.component.html</context>
          <context context-type="linenumber">40,41</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-ui-kit/src/lib/components/form-controls/text-input/text-input.component.html</context>
          <context context-type="linenumber">44,46</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-ui-kit/src/lib/components/modal/modal.component.html</context>
          <context context-type="linenumber">10,11</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/how-to-sign-waiver/how-to-sign-waiver.component.html</context>
          <context context-type="linenumber">4,5</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/participant-section/participant-section.component.html</context>
          <context context-type="linenumber">4,5</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/select-participants/select-participants.component.html</context>
          <context context-type="linenumber">21,22</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/select-participants/select-participants.component.html</context>
          <context context-type="linenumber">42,43</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/tag/tag.component.html</context>
          <context context-type="linenumber">10,11</context>
        </context-group>
        <note priority="1" from="description">login-form.login-button</note>
      </trans-unit>
      <trans-unit id="8164791295086777340" datatype="html">
        <source>Don&apos;t have an account?</source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-auth/src/lib/components/login-form/login-form.component.html</context>
          <context context-type="linenumber">52,53</context>
        </context-group>
        <note priority="1" from="description">login-form.dont-have-account</note>
      </trans-unit>
      <trans-unit id="7028204981963450021" datatype="html">
        <source>Sign up</source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-auth/src/lib/components/login-form/login-form.component.html</context>
          <context context-type="linenumber">53,55</context>
        </context-group>
        <note priority="1" from="description">login-form.sign-up</note>
      </trans-unit>
      <trans-unit id="5277739594710154939" datatype="html">
        <source>Already have an account?</source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-auth/src/lib/components/registration-form-base/registration-form-base.component.html</context>
          <context context-type="linenumber">98,99</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/welcome-to-waivers/welcome-to-waivers.component.html</context>
          <context context-type="linenumber">76,78</context>
        </context-group>
        <note priority="1" from="description">registration-form-base.already-have-account</note>
      </trans-unit>
      <trans-unit id="2454050363478003966" datatype="html">
        <source>Login</source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-auth/src/lib/components/registration-form-base/registration-form-base.component.html</context>
          <context context-type="linenumber">99,101</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-auth/src/lib/components/reset-password/reset-password.component.html</context>
          <context context-type="linenumber">24,27</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/auth/auth.component.html</context>
          <context context-type="linenumber">51,52</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/welcome-to-waivers/welcome-to-waivers.component.html</context>
          <context context-type="linenumber">82,85</context>
        </context-group>
        <note priority="1" from="description">registration-form-base.login</note>
      </trans-unit>
      <trans-unit id="8950261455381068073" datatype="html">
        <source>Create a new password</source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-auth/src/lib/components/reset-password/reset-password.component.html</context>
          <context context-type="linenumber">4,5</context>
        </context-group>
        <note priority="1" from="description">reset-password.create-new-password</note>
      </trans-unit>
      <trans-unit id="2545262148343261844" datatype="html">
        <source>Your new password must be different from the previous password.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-auth/src/lib/components/reset-password/reset-password.component.html</context>
          <context context-type="linenumber">5,7</context>
        </context-group>
        <note priority="1" from="description">reset-password.your-new-password-must-be-different-from-the-previous-password</note>
      </trans-unit>
      <trans-unit id="3241357959735682038" datatype="html">
        <source>Confirm Password</source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-auth/src/lib/components/reset-password/reset-password.component.html</context>
          <context context-type="linenumber">18,22</context>
        </context-group>
        <note priority="1" from="description">reset-password.confirm-password</note>
      </trans-unit>
      <trans-unit id="327534766307158513" datatype="html">
        <source>Remembered it?</source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-auth/src/lib/components/reset-password/reset-password.component.html</context>
          <context context-type="linenumber">23,24</context>
        </context-group>
        <note priority="1" from="description">reset-password.remembered-it</note>
      </trans-unit>
      <trans-unit id="7619560701830330401" datatype="html">
        <source>Loading</source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-auth/src/lib/components/reset-password/reset-password.component.html</context>
          <context context-type="linenumber">28,29</context>
        </context-group>
        <note priority="1" from="description">reset-password.loading</note>
      </trans-unit>
      <trans-unit id="4648900870671159218" datatype="html">
        <source>Success</source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-auth/src/lib/components/status-result/status-result.component.html</context>
          <context context-type="linenumber">9,11</context>
        </context-group>
        <note priority="1" from="description">status-result.success</note>
      </trans-unit>
      <trans-unit id="7256395947475975935" datatype="html">
        <source>Failed</source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-auth/src/lib/components/status-result/status-result.component.html</context>
          <context context-type="linenumber">14,16</context>
        </context-group>
        <note priority="1" from="description">status-result.failed</note>
      </trans-unit>
      <trans-unit id="466492426610382126" datatype="html">
        <source> <x id="INTERPOLATION" equiv-text="{{ queryParams.redirectLabel }}"/></source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-auth/src/lib/components/status-result/status-result.component.html</context>
          <context context-type="linenumber">27,28</context>
        </context-group>
        <note priority="1" from="description">status-result.redirect-label</note>
      </trans-unit>
      <trans-unit id="2127032578120864096" datatype="html">
        <source>My Profile</source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-my-account/src/lib/components/profile/profile.component.html</context>
          <context context-type="linenumber">3,5</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-my-account/src/lib/components/profile/profile.component.html</context>
          <context context-type="linenumber">9,10</context>
        </context-group>
        <note priority="1" from="description">profile.title</note>
      </trans-unit>
      <trans-unit id="6028371114637047813" datatype="html">
        <source>First Name</source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-my-account/src/lib/components/profile/profile.component.html</context>
          <context context-type="linenumber">28,29</context>
        </context-group>
        <note priority="1" from="description">profile.firstName</note>
      </trans-unit>
      <trans-unit id="4407559560004943843" datatype="html">
        <source>Last Name</source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-my-account/src/lib/components/profile/profile.component.html</context>
          <context context-type="linenumber">33,34</context>
        </context-group>
        <note priority="1" from="description">profile.lastName</note>
      </trans-unit>
      <trans-unit id="7890419814061896884" datatype="html">
        <source>Date of Birth</source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-my-account/src/lib/components/profile/profile.component.html</context>
          <context context-type="linenumber">38,39</context>
        </context-group>
        <note priority="1" from="description">profile.dateOfBirth</note>
      </trans-unit>
      <trans-unit id="4879517228803151475" datatype="html">
        <source>Email Address</source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-my-account/src/lib/components/profile/profile.component.html</context>
          <context context-type="linenumber">43,44</context>
        </context-group>
        <note priority="1" from="description">profile.emailAddress</note>
      </trans-unit>
      <trans-unit id="7571110135159465275" datatype="html">
        <source>Mobile Number</source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-my-account/src/lib/components/profile/profile.component.html</context>
          <context context-type="linenumber">48,49</context>
        </context-group>
        <note priority="1" from="description">profile.mobileNumber</note>
      </trans-unit>
      <trans-unit id="8899299475157873749" datatype="html">
        <source><x id="START_TAG_DIV" ctype="x-div" equiv-text="&lt;div [innerHTML]=&quot;label()&quot;&gt;"/><x id="CLOSE_TAG_DIV" ctype="x-div" equiv-text="&lt;/div&gt;"/><x id="START_BLOCK_IF" equiv-text="@if (isRequired) {"/><x id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span class=&quot;text-red-500&quot;&gt;"/>*<x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span&gt;"/><x id="CLOSE_BLOCK_IF" equiv-text="}"/></source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-ui-kit/src/lib/components/form-controls/checkbox/checkbox.component.html</context>
          <context context-type="linenumber">16,20</context>
        </context-group>
        <note priority="1" from="description">checkbox.label</note>
      </trans-unit>
      <trans-unit id="3855643343013711110" datatype="html">
        <source> <x id="INTERPOLATION" equiv-text="{{ label() }}"/> <x id="START_BLOCK_IF" equiv-text="@if (isRequired) {"/><x id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span class=&quot;text-red-500&quot;&gt;"/>*<x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span&gt;"/><x id="CLOSE_BLOCK_IF" equiv-text="}"/></source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-ui-kit/src/lib/components/form-controls/date-picker/date-picker.component.html</context>
          <context context-type="linenumber">6,11</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-ui-kit/src/lib/components/form-controls/phone-input/phone-input.component.html</context>
          <context context-type="linenumber">5,10</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-ui-kit/src/lib/components/form-controls/select/select.component.html</context>
          <context context-type="linenumber">3,8</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-ui-kit/src/lib/components/form-controls/text-input/text-input.component.html</context>
          <context context-type="linenumber">4,9</context>
        </context-group>
        <note priority="1" from="description">date-picker.label</note>
      </trans-unit>
      <trans-unit id="7747422763967814444" datatype="html">
        <source> No countries found </source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-ui-kit/src/lib/components/form-controls/phone-input/phone-input.component.html</context>
          <context context-type="linenumber">48,49</context>
        </context-group>
        <note priority="1" from="description">phone-input.no-countries-found</note>
      </trans-unit>
      <trans-unit id="923254389855448472" datatype="html">
        <source> Please enter a valid phone number.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-ui-kit/src/lib/components/form-controls/phone-input/phone-input.component.html</context>
          <context context-type="linenumber">65,66</context>
        </context-group>
        <note priority="1" from="description">phone-input.error-message</note>
      </trans-unit>
      <trans-unit id="110427558038306338" datatype="html">
        <source>Examples: <x id="INTERPOLATION" equiv-text="{{ getFormatExamples() }}"/></source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-ui-kit/src/lib/components/form-controls/phone-input/phone-input.component.html</context>
          <context context-type="linenumber">67,69</context>
        </context-group>
        <note priority="1" from="description">phone-input.error-message</note>
      </trans-unit>
      <trans-unit id="8585958577229523205" datatype="html">
        <source> No options available </source>
        <context-group purpose="location">
          <context context-type="sourcefile">lib/lib-ui-kit/src/lib/components/form-controls/select/select.component.html</context>
          <context context-type="linenumber">41,42</context>
        </context-group>
        <note priority="1" from="description">select.no-options-available</note>
      </trans-unit>
      <trans-unit id="2081157122786584775" datatype="html">
        <source>Minor <x id="INTERPOLATION" equiv-text="{{ index() + 1 }}"/></source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/add-minor-form/add-minor-form.default.component.html</context>
          <context context-type="linenumber">3,4</context>
        </context-group>
        <note priority="1" from="description">add-minor-form.minor-title</note>
      </trans-unit>
      <trans-unit id="7480189852559050941" datatype="html">
        <source>Sign waivers</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/auth/auth.component.html</context>
          <context context-type="linenumber">6,7</context>
        </context-group>
        <note priority="1" from="description">auth.sign-waivers</note>
      </trans-unit>
      <trans-unit id="3494300386175238017" datatype="html">
        <source> Sign waivers to get access to the services. </source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/auth/auth.component.html</context>
          <context context-type="linenumber">8,9</context>
        </context-group>
        <note priority="1" from="description">auth.sign-waivers-description</note>
      </trans-unit>
      <trans-unit id="6627939055631574589" datatype="html">
        <source> How to sign waivers?</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/auth/auth.component.html</context>
          <context context-type="linenumber">15,17</context>
        </context-group>
        <note priority="1" from="description">auth.how-to-sign-waivers</note>
      </trans-unit>
      <trans-unit id="8881376606363030589" datatype="html">
        <source>Preview waiver</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/auth/auth.component.html</context>
          <context context-type="linenumber">20,21</context>
        </context-group>
        <note priority="1" from="description">auth.preview-waiver</note>
      </trans-unit>
      <trans-unit id="2602451928058194946" datatype="html">
        <source>New customer?</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/auth/auth.component.html</context>
          <context context-type="linenumber">37,38</context>
        </context-group>
        <note priority="1" from="description">auth.new-customer</note>
      </trans-unit>
      <trans-unit id="3301086086650990787" datatype="html">
        <source>Register</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/auth/auth.component.html</context>
          <context context-type="linenumber">38,39</context>
        </context-group>
        <note priority="1" from="description">auth.register</note>
      </trans-unit>
      <trans-unit id="2693719632541439826" datatype="html">
        <source> Existing customer? </source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/auth/auth.component.html</context>
          <context context-type="linenumber">49,50</context>
        </context-group>
        <note priority="1" from="description">auth.existing-customer</note>
      </trans-unit>
      <trans-unit id="5990315732389421367" datatype="html">
        <source>Step <x id="INTERPOLATION" equiv-text="{{ item.step }}"/></source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/how-to-sign-waiver/how-to-sign-waiver.component.html</context>
          <context context-type="linenumber">3,4</context>
        </context-group>
        <note priority="1" from="description">how-to-sign-waiver.step</note>
      </trans-unit>
      <trans-unit id="5953633535667036596" datatype="html">
        <source>Add Minor</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/registration-form-waiver/registration-form-waiver.component.html</context>
          <context context-type="linenumber">29,32</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/pages/my-account/my-relations/my-relations.component.html</context>
          <context context-type="linenumber">64,66</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/pages/my-account/my-relations/my-relations.component.html</context>
          <context context-type="linenumber">81,83</context>
        </context-group>
        <note priority="1" from="description">registration-form-waiver.add-minor</note>
      </trans-unit>
      <trans-unit id="5376841124151957389" datatype="html">
        <source> Sign waiver - Rollercoaster23 </source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/sign-waiver/sign-waiver.component.html</context>
          <context context-type="linenumber">3,6</context>
        </context-group>
        <note priority="1" from="description">sign-waiver.title</note>
      </trans-unit>
      <trans-unit id="4454051842729819184" datatype="html">
        <source> Participants (<x id="INTERPOLATION" equiv-text="{{ getNumberOfParticipants() }}"/>) </source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/sign-waiver/sign-waiver.component.html</context>
          <context context-type="linenumber">11,12</context>
        </context-group>
        <note priority="1" from="description">sign-waiver.participants</note>
      </trans-unit>
      <trans-unit id="1497642410570668904" datatype="html">
        <source> Add or remove participants </source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/sign-waiver/sign-waiver.component.html</context>
          <context context-type="linenumber">19,21</context>
        </context-group>
        <note priority="1" from="description">sign-waiver.add-or-remove-participants</note>
      </trans-unit>
      <trans-unit id="5786202362857657863" datatype="html">
        <source> Read and sign waiver </source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/sign-waiver/sign-waiver.component.html</context>
          <context context-type="linenumber">29,31</context>
        </context-group>
        <note priority="1" from="description">sign-waiver.read-and-sign-waiver</note>
      </trans-unit>
      <trans-unit id="8942137918196647882" datatype="html">
        <source> Please read the waiver carefully and sign at the end of the document. </source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/sign-waiver/sign-waiver.component.html</context>
          <context context-type="linenumber">32,36</context>
        </context-group>
        <note priority="1" from="description">sign-waiver.read-and-sign-waiver-description</note>
      </trans-unit>
      <trans-unit id="5664248039379431876" datatype="html">
        <source>Sign Waiver</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/sign-waiver/sign-waiver.component.html</context>
          <context context-type="linenumber">46,48</context>
        </context-group>
        <note priority="1" from="description">sign-waiver.sign-waiver-button</note>
      </trans-unit>
      <trans-unit id="194622474439904530" datatype="html">
        <source> Waiver signed successfully </source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/signed-waiver-confirmation/signed-waiver-confirmation.component.html</context>
          <context context-type="linenumber">6,8</context>
        </context-group>
        <note priority="1" from="description">signed-waiver-confirmation.title</note>
      </trans-unit>
      <trans-unit id="7311229686743032949" datatype="html">
        <source> Use the waiver code to avail the service. We have also sent the waiver code on your email and SMS. </source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/signed-waiver-confirmation/signed-waiver-confirmation.component.html</context>
          <context context-type="linenumber">9,11</context>
        </context-group>
        <note priority="1" from="description">signed-waiver-confirmation.description</note>
      </trans-unit>
      <trans-unit id="792595714652974629" datatype="html">
        <source>Waiver Code</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/signed-waiver-confirmation/signed-waiver-confirmation.component.html</context>
          <context context-type="linenumber">17,18</context>
        </context-group>
        <note priority="1" from="description">signed-waiver-confirmation.waiver-code-label</note>
      </trans-unit>
      <trans-unit id="7898342258115643190" datatype="html">
        <source>Download waiver document</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/signed-waiver-confirmation/signed-waiver-confirmation.component.html</context>
          <context context-type="linenumber">28,30</context>
        </context-group>
        <note priority="1" from="description">signed-waiver-confirmation.download-waiver-button</note>
      </trans-unit>
      <trans-unit id="605921802959953355" datatype="html">
        <source>Sign for more</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/signed-waiver-confirmation/signed-waiver-confirmation.component.html</context>
          <context context-type="linenumber">32,34</context>
        </context-group>
        <note priority="1" from="description">signed-waiver-confirmation.sign-for-more-button</note>
      </trans-unit>
      <trans-unit id="9155373527290624750" datatype="html">
        <source>View signed waiver</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/signed-waiver-confirmation/signed-waiver-confirmation.component.html</context>
          <context context-type="linenumber">36,38</context>
        </context-group>
        <note priority="1" from="description">signed-waiver-confirmation.view-signed-waiver-button</note>
      </trans-unit>
      <trans-unit id="3797778920049399855" datatype="html">
        <source>Logout</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/signed-waiver-confirmation/signed-waiver-confirmation.component.html</context>
          <context context-type="linenumber">39,42</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/shared/mobile-sidebar/mobile-sidebar.component.html</context>
          <context context-type="linenumber">70,72</context>
        </context-group>
        <note priority="1" from="description">signed-waiver-confirmation.logout-button</note>
      </trans-unit>
      <trans-unit id="1715239865660054779" datatype="html">
        <source>Select your preferred location</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/site-selection/site-selection.component.html</context>
          <context context-type="linenumber">4,7</context>
        </context-group>
        <note priority="1" from="description">site-selection.title</note>
      </trans-unit>
      <trans-unit id="5462054841217944503" datatype="html">
        <source>Updating site...</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/site-selection/site-selection.component.html</context>
          <context context-type="linenumber">36,38</context>
        </context-group>
        <note priority="1" from="description">site-selection.updating-site</note>
      </trans-unit>
      <trans-unit id="1645193489541371451" datatype="html">
        <source>No results found</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/site-selection/site-selection.component.html</context>
          <context context-type="linenumber">53,55</context>
        </context-group>
        <note priority="1" from="description">site-selection.no-results-title</note>
      </trans-unit>
      <trans-unit id="5768831122039752830" datatype="html">
        <source> The location you entered is invalid, incorrect, or currently outside our service area. Please try again. </source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/site-selection/site-selection.component.html</context>
          <context context-type="linenumber">56,58</context>
        </context-group>
        <note priority="1" from="description">site-selection.no-results-description</note>
      </trans-unit>
      <trans-unit id="6333985921958861333" datatype="html">
        <source>I agree to the </source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/terms-and-conditions/terms-and-conditions.component.html</context>
          <context context-type="linenumber">11,13</context>
        </context-group>
        <note priority="1" from="description">terms-and-conditions.i-agree-to-the-text</note>
      </trans-unit>
      <trans-unit id="7679446686075090659" datatype="html">
        <source> Welcome to Semnox waiver </source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/welcome-to-waivers/welcome-to-waivers.component.html</context>
          <context context-type="linenumber">9,11</context>
        </context-group>
        <note priority="1" from="description">welcome-to-waivers.title</note>
      </trans-unit>
      <trans-unit id="7731019333902179708" datatype="html">
        <source> To enjoy our services, each guest needs to sign a waiver. Don’t worry—it’s quick and easy! Just follow these simple steps: </source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/welcome-to-waivers/welcome-to-waivers.component.html</context>
          <context context-type="linenumber">12,15</context>
        </context-group>
        <note priority="1" from="description">welcome-to-waivers.description</note>
      </trans-unit>
      <trans-unit id="2589199309811230272" datatype="html">
        <source> Step <x id="INTERPOLATION" equiv-text="{{ $index + 1 }}"/> </source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/welcome-to-waivers/welcome-to-waivers.component.html</context>
          <context context-type="linenumber">30,31</context>
        </context-group>
        <note priority="1" from="description">welcome-to-waivers.step-number</note>
      </trans-unit>
      <trans-unit id="785004611720562779" datatype="html">
        <source>Register and sign waiver</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/welcome-to-waivers/welcome-to-waivers.component.html</context>
          <context context-type="linenumber">73,75</context>
        </context-group>
        <note priority="1" from="description">welcome-to-waivers.register-button</note>
      </trans-unit>
      <trans-unit id="welcome-to-waivers.step1.title" datatype="html">
        <source>Login to your account</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/welcome-to-waivers/welcome-to-waivers.component.ts</context>
          <context context-type="linenumber">25</context>
        </context-group>
        <note priority="1" from="description">welcome-to-waivers.step1.title</note>
      </trans-unit>
      <trans-unit id="welcome-to-waivers.step1.description" datatype="html">
        <source>Log in to your account or register to get started.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/welcome-to-waivers/welcome-to-waivers.component.ts</context>
          <context context-type="linenumber">26</context>
        </context-group>
        <note priority="1" from="description">welcome-to-waivers.step1.description</note>
      </trans-unit>
      <trans-unit id="welcome-to-waivers.step2.title" datatype="html">
        <source>Select the waiver and sign</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/welcome-to-waivers/welcome-to-waivers.component.ts</context>
          <context context-type="linenumber">31</context>
        </context-group>
        <note priority="1" from="description">welcome-to-waivers.step2.title</note>
      </trans-unit>
      <trans-unit id="welcome-to-waivers.step2.description" datatype="html">
        <source>Sign for yourself and your dependents (family &amp; friends) to receive waiver acknowledgment.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/welcome-to-waivers/welcome-to-waivers.component.ts</context>
          <context context-type="linenumber">32</context>
        </context-group>
        <note priority="1" from="description">welcome-to-waivers.step2.description</note>
      </trans-unit>
      <trans-unit id="welcome-to-waivers.step3.title" datatype="html">
        <source>Check-in online</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/welcome-to-waivers/welcome-to-waivers.component.ts</context>
          <context context-type="linenumber">37</context>
        </context-group>
        <note priority="1" from="description">welcome-to-waivers.step3.title</note>
      </trans-unit>
      <trans-unit id="welcome-to-waivers.step3.description" datatype="html">
        <source>Enter the code/OTP received during waiver signing and proceed</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/components/welcome-to-waivers/welcome-to-waivers.component.ts</context>
          <context context-type="linenumber">38</context>
        </context-group>
        <note priority="1" from="description">welcome-to-waivers.step3.description</note>
      </trans-unit>
      <trans-unit id="select-participants.page-title" datatype="html">
        <source>Select participants</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/constants/customer.constant.ts</context>
          <context context-type="linenumber">12</context>
        </context-group>
        <note priority="1" from="description">select-participants.page-title</note>
      </trans-unit>
      <trans-unit id="select-participants.section-title" datatype="html">
        <source>Select members to sign</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/constants/customer.constant.ts</context>
          <context context-type="linenumber">13</context>
        </context-group>
        <note priority="1" from="description">select-participants.section-title</note>
      </trans-unit>
      <trans-unit id="select-participants.primary-account-holder" datatype="html">
        <source>Primary account holder</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/constants/customer.constant.ts</context>
          <context context-type="linenumber">14</context>
        </context-group>
        <note priority="1" from="description">select-participants.primary-account-holder</note>
      </trans-unit>
      <trans-unit id="select-participants.minors" datatype="html">
        <source>Minors</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/constants/customer.constant.ts</context>
          <context context-type="linenumber">15</context>
        </context-group>
        <note priority="1" from="description">select-participants.minors</note>
      </trans-unit>
      <trans-unit id="select-participants.minors-description" datatype="html">
        <source>Minors info should be added in order to selecting them for signing.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/constants/customer.constant.ts</context>
          <context context-type="linenumber">16</context>
        </context-group>
        <note priority="1" from="description">select-participants.minors-description</note>
      </trans-unit>
      <trans-unit id="select-participants.add-minor" datatype="html">
        <source>Add Minor</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/constants/customer.constant.ts</context>
          <context context-type="linenumber">17</context>
        </context-group>
        <note priority="1" from="description">select-participants.add-minor</note>
      </trans-unit>
      <trans-unit id="select-participants.sign-waiver" datatype="html">
        <source>Sign Waiver</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/constants/customer.constant.ts</context>
          <context context-type="linenumber">18</context>
        </context-group>
        <note priority="1" from="description">select-participants.sign-waiver</note>
      </trans-unit>
      <trans-unit id="select-participants.already-have-account" datatype="html">
        <source>Already have an account?</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/constants/customer.constant.ts</context>
          <context context-type="linenumber">19</context>
        </context-group>
        <note priority="1" from="description">select-participants.already-have-account</note>
      </trans-unit>
      <trans-unit id="select-participants.login" datatype="html">
        <source>Login</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/constants/customer.constant.ts</context>
          <context context-type="linenumber">20</context>
        </context-group>
        <note priority="1" from="description">select-participants.login</note>
      </trans-unit>
      <trans-unit id="3768927257183755959" datatype="html">
        <source>Save</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/pages/my-account/add-minor-form-modal/add-minor-form-modal.component.html</context>
          <context context-type="linenumber">18,21</context>
        </context-group>
        <note priority="1" from="description">add-minor-form-modal.save-button</note>
      </trans-unit>
      <trans-unit id="3323779912804193436" datatype="html">
        <source>My Relations</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/pages/my-account/my-relations/my-relations.component.html</context>
          <context context-type="linenumber">4,6</context>
        </context-group>
        <note priority="1" from="description">my-relations.title</note>
      </trans-unit>
      <trans-unit id="8601816566809330139" datatype="html">
        <source>My Signed Waivers</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/pages/my-account/my-signed-waivers/my-signed-waivers.component.html</context>
          <context context-type="linenumber">4,6</context>
        </context-group>
        <note priority="1" from="description">my-signed-waivers.title</note>
      </trans-unit>
      <trans-unit id="form-validation-message.required" datatype="html">
        <source><x id="PH" equiv-text="field.label"/> is required</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/services/buisiness-layer/customer-ui-metadata-bl.service.ts</context>
          <context context-type="linenumber">573</context>
        </context-group>
        <note priority="1" from="description">form-validation-message.required</note>
      </trans-unit>
      <trans-unit id="header.home" datatype="html">
        <source>Home</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/shared/header/header.component.ts</context>
          <context context-type="linenumber">68</context>
        </context-group>
        <note priority="1" from="description">header.home</note>
      </trans-unit>
      <trans-unit id="header.cards" datatype="html">
        <source>Cards</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/shared/header/header.component.ts</context>
          <context context-type="linenumber">69</context>
        </context-group>
        <note priority="1" from="description">header.cards</note>
      </trans-unit>
      <trans-unit id="header.waivers" datatype="html">
        <source>Waivers</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/shared/header/header.component.ts</context>
          <context context-type="linenumber">71</context>
        </context-group>
        <note priority="1" from="description">header.waivers</note>
      </trans-unit>
      <trans-unit id="header.f-and-b" datatype="html">
        <source>F&amp;B</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/shared/header/header.component.ts</context>
          <context context-type="linenumber">76</context>
        </context-group>
        <note priority="1" from="description">header.f-and-b</note>
      </trans-unit>
      <trans-unit id="header.recharge" datatype="html">
        <source>Recharge</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/shared/header/header.component.ts</context>
          <context context-type="linenumber">81</context>
        </context-group>
        <note priority="1" from="description">header.recharge</note>
      </trans-unit>
      <trans-unit id="header.reservations" datatype="html">
        <source>Reservations</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/shared/header/header.component.ts</context>
          <context context-type="linenumber">86</context>
        </context-group>
        <note priority="1" from="description">header.reservations</note>
      </trans-unit>
      <trans-unit id="header.my-profile" datatype="html">
        <source>My Profile</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/shared/header/header.component.ts</context>
          <context context-type="linenumber">104</context>
        </context-group>
        <note priority="1" from="description">header.my-profile</note>
      </trans-unit>
      <trans-unit id="header.my-cards" datatype="html">
        <source>My Cards</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/shared/header/header.component.ts</context>
          <context context-type="linenumber">105</context>
        </context-group>
        <note priority="1" from="description">header.my-cards</note>
      </trans-unit>
      <trans-unit id="header.my-subscriptions" datatype="html">
        <source>My Subscriptions</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/shared/header/header.component.ts</context>
          <context context-type="linenumber">106</context>
        </context-group>
        <note priority="1" from="description">header.my-subscriptions</note>
      </trans-unit>
      <trans-unit id="header.my-signed-waivers" datatype="html">
        <source>My Signed Waivers</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/shared/header/header.component.ts</context>
          <context context-type="linenumber">107</context>
        </context-group>
        <note priority="1" from="description">header.my-signed-waivers</note>
      </trans-unit>
      <trans-unit id="header.my-relations" datatype="html">
        <source>My Relations</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/shared/header/header.component.ts</context>
          <context context-type="linenumber">108</context>
        </context-group>
        <note priority="1" from="description">header.my-relations</note>
      </trans-unit>
      <trans-unit id="header.my-orders" datatype="html">
        <source>My Orders</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/shared/header/header.component.ts</context>
          <context context-type="linenumber">109</context>
        </context-group>
        <note priority="1" from="description">header.my-orders</note>
      </trans-unit>
      <trans-unit id="header.change-password" datatype="html">
        <source>Change Password</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/shared/header/header.component.ts</context>
          <context context-type="linenumber">110</context>
        </context-group>
        <note priority="1" from="description">header.change-password</note>
      </trans-unit>
      <trans-unit id="header.logout" datatype="html">
        <source>Logout</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/shared/header/header.component.ts</context>
          <context context-type="linenumber">111</context>
        </context-group>
        <note priority="1" from="description">header.logout</note>
      </trans-unit>
      <trans-unit id="4818852650817502025" datatype="html">
        <source>My Account</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/shared/header/header.default.component.html</context>
          <context context-type="linenumber">41,42</context>
        </context-group>
        <note priority="1" from="description">header.default.my-account</note>
      </trans-unit>
      <trans-unit id="mobile-sidebar.home" datatype="html">
        <source>Home</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/shared/mobile-sidebar/mobile-sidebar.component.ts</context>
          <context context-type="linenumber">62</context>
        </context-group>
        <note priority="1" from="description">mobile-sidebar.home</note>
      </trans-unit>
      <trans-unit id="mobile-sidebar.cards" datatype="html">
        <source>Cards</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/shared/mobile-sidebar/mobile-sidebar.component.ts</context>
          <context context-type="linenumber">67</context>
        </context-group>
        <note priority="1" from="description">mobile-sidebar.cards</note>
      </trans-unit>
      <trans-unit id="mobile-sidebar.waivers" datatype="html">
        <source>Waivers</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/shared/mobile-sidebar/mobile-sidebar.component.ts</context>
          <context context-type="linenumber">72</context>
        </context-group>
        <note priority="1" from="description">mobile-sidebar.waivers</note>
      </trans-unit>
      <trans-unit id="mobile-sidebar.sign-waiver" datatype="html">
        <source>Sign Waiver</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/shared/mobile-sidebar/mobile-sidebar.component.ts</context>
          <context context-type="linenumber">78</context>
        </context-group>
        <note priority="1" from="description">mobile-sidebar.sign-waiver</note>
      </trans-unit>
      <trans-unit id="mobile-sidebar.view-signed-waivers" datatype="html">
        <source>View Signed Waivers</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/shared/mobile-sidebar/mobile-sidebar.component.ts</context>
          <context context-type="linenumber">83</context>
        </context-group>
        <note priority="1" from="description">mobile-sidebar.view-signed-waivers</note>
      </trans-unit>
      <trans-unit id="mobile-sidebar.view-relations" datatype="html">
        <source>View Relations</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/shared/mobile-sidebar/mobile-sidebar.component.ts</context>
          <context context-type="linenumber">88</context>
        </context-group>
        <note priority="1" from="description">mobile-sidebar.view-relations</note>
      </trans-unit>
      <trans-unit id="mobile-sidebar.food-and-beverages" datatype="html">
        <source>Food &amp; Beverages</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/shared/mobile-sidebar/mobile-sidebar.component.ts</context>
          <context context-type="linenumber">95</context>
        </context-group>
        <note priority="1" from="description">mobile-sidebar.food-and-beverages</note>
      </trans-unit>
      <trans-unit id="mobile-sidebar.recharge" datatype="html">
        <source>Recharge</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/shared/mobile-sidebar/mobile-sidebar.component.ts</context>
          <context context-type="linenumber">100</context>
        </context-group>
        <note priority="1" from="description">mobile-sidebar.recharge</note>
      </trans-unit>
      <trans-unit id="mobile-sidebar.party-reservations" datatype="html">
        <source>Party Reservations</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/shared/mobile-sidebar/mobile-sidebar.component.ts</context>
          <context context-type="linenumber">105</context>
        </context-group>
        <note priority="1" from="description">mobile-sidebar.party-reservations</note>
      </trans-unit>
      <trans-unit id="mobile-sidebar.my-account" datatype="html">
        <source>My Account</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/shared/mobile-sidebar/mobile-sidebar.component.ts</context>
          <context context-type="linenumber">110</context>
        </context-group>
        <note priority="1" from="description">mobile-sidebar.my-account</note>
      </trans-unit>
      <trans-unit id="mobile-sidebar.my-profile" datatype="html">
        <source>My Profile</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/shared/mobile-sidebar/mobile-sidebar.component.ts</context>
          <context context-type="linenumber">115</context>
        </context-group>
        <note priority="1" from="description">mobile-sidebar.my-profile</note>
      </trans-unit>
      <trans-unit id="mobile-sidebar.my-cards" datatype="html">
        <source>My Cards</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/shared/mobile-sidebar/mobile-sidebar.component.ts</context>
          <context context-type="linenumber">119</context>
        </context-group>
        <note priority="1" from="description">mobile-sidebar.my-cards</note>
      </trans-unit>
      <trans-unit id="mobile-sidebar.my-subscriptions" datatype="html">
        <source>My Subscriptions</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/shared/mobile-sidebar/mobile-sidebar.component.ts</context>
          <context context-type="linenumber">121</context>
        </context-group>
        <note priority="1" from="description">mobile-sidebar.my-subscriptions</note>
      </trans-unit>
      <trans-unit id="mobile-sidebar.my-orders" datatype="html">
        <source>My Orders</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/shared/mobile-sidebar/mobile-sidebar.component.ts</context>
          <context context-type="linenumber">125</context>
        </context-group>
        <note priority="1" from="description">mobile-sidebar.my-orders</note>
      </trans-unit>
      <trans-unit id="mobile-sidebar.change-password" datatype="html">
        <source>Change Password</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/shared/mobile-sidebar/mobile-sidebar.component.ts</context>
          <context context-type="linenumber">127</context>
        </context-group>
        <note priority="1" from="description">mobile-sidebar.change-password</note>
      </trans-unit>
      <trans-unit id="mobile-sidebar.logout" datatype="html">
        <source>Logout</source>
        <context-group purpose="location">
          <context context-type="sourcefile">projects/online-waiver/src/app/shared/mobile-sidebar/mobile-sidebar.component.ts</context>
          <context context-type="linenumber">132</context>
        </context-group>
        <note priority="1" from="description">mobile-sidebar.logout</note>
      </trans-unit>
    </body>
  </file>
</xliff>
