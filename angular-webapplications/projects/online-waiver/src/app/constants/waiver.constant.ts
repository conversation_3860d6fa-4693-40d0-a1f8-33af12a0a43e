import { appCoreConstant } from 'lib-app-core';

export const waiverConstants = {
    ...appCoreConstant,
    API_ENDPOINTS: {
        ...appCoreConstant.API_ENDPOINTS,
        GET_WAIVER_SET_CONTAINER: '/Customer/Waiver/WaiverSetContainer',
        VALIDATE_HTML_WAIVER: '/Product/Waivers/VaidateHTMLWaiver/Customers',
        CREATE_CUSTOMER_WAIVER:
            '/Customer/{custId}/Waiver/{selectedWaiverSetId}/CreateCustomerWaiver?previewMode=true&postMode=false',
        USER_LOGIN: '/v2/Customer/CustomerLogin',
        FORGOT_PASSWORD: '/v2/Customer/ForgotPasswordRequest',
        VALIDATE_TOKEN: '/Security/SecurityTokens',
        USER_DETAILS: '/Customer/Customers?customerGUID={CustomerId}&buildChildRecords=true',
        PASSWORD_RESET: '/v2/Customer/ResetPasswordRequest',
        GET_SIGNED_WAIVERS: '/Customer/Waiver/SignedWaivers',
        CUSTOMER_RELATION:
            '/Customer/CustomerRelationships?customerId={CustomerId}&buildChildRecords=true',
        PRIMARY_RELATION:
            '/Customer/Customers?customerId={CustomerId}&buildChildRecords=true',
        GET_TRANSACTION_DETAILS:
            '/Transaction/Transactions?transactionGuid={transactionGuid}&buildChildRecords=true',
        GET_ALL_WAIVER_SET:
            '/Customer/Waiver/WaiverSetContainer?siteId={siteId}&languageId={languageId}',
        GET_TRANSACTION_TIME:
            '/Transaction/Reservations?transactionId={transactionId}',
        CREATE_DEFAULT_CUSTOMER: '/Product/Waivers/VaidateHTMLWaiver/Customers',
        GET_HTML_WAIVER:
            '/Customer/{DefaultCustomer}/Waiver/{WaiverSetId}/CreateCustomerWaiver?previewMode=true&postMode=false',
        POST_PDF_WAIVER:
            '/Customer/{CustomerId}/Waiver/{WaiverSetId}/CreateCustomerSignedWaiver',
    },
    SAMPLE_WAIVER: 'Test Waiver',
    EXCLUDED_MINOR_FIELDS: [
        'ADDRESS1',
        'ADDRESS2',
        'ADDRESS3',
        'CITY',
        'STATE',
        'COUNTRY',
        'PIN',
        'EMAIL',
        'ANNIVERSARY',
        'CONTACT_PHONE',
        'ADDRESS_CONTACT_PHONE',
        'OPT_IN_PROMOTIONS',
        'OPT_IN_PROMOTIONS_MODE',
        'OPT_OUT_WHATSAPP_MESSAGE',
    ],
    CUSTOMER_TYPE: {
        MAJOR: 'major',
        MINOR: 'minor',
    },
    INPUT_FIELD_TYPE: {
        TEXT: 'text',
        DATE: 'date',
        EMAIL: 'email',
        NUMBER: 'number',
        FLAG: 'checkbox',
        LIST: 'select',
        PHONE: 'phone',
    },
    VALIDATION_TYPE: {
        MANDATORY: 'M',
        OPTIONAL: 'O',
    },

    AGE_OF_MAJORITY: 'AGE_OF_MAJORITY',

    CUSTOMER_FIELD_OVERRIDDEN_ORDER: {
        TITLE: 1,
        CUSTOMER_NAME: 2,
        MIDDLE_NAME: 3,
        LAST_NAME: 4,
        NICKNAME: 5,
        GENDER: 6,
        BIRTH_DATE: 7,
        ANNIVERSARY: 8,
        CONTACT_PHONE: 9,
        ADDRESS_CONTACT_PHONE: 10,
        EMAIL_SUBTYPE: 11,
        PHONE_SUBTYPE: 12,
        ADDRESS_PHONE_COUNTRY_CODE: 13,
        ADDRESS_PHONE_SUBTYPE: 14,
        ADDRESS1: 15,
        ADDRESS2: 16,
        ADDRESS3: 17,
        CITY: 18,
        COUNTRY: 19,
        STATE: 20,
        PIN: 21,
        ADDRESS_TYPE: 22,
        UNIQUE_ID: 23,
        FBUSERID: 24,
        FBACCESSTOKEN: 25,
        TWACCESSTOKEN: 26,
        TWACCESSSECRET: 27,
        WECHAT_ACCESS_TOKEN: 28,
        COMPANY: 29,
        DESIGNATION: 30,
        TAXCODE: 31,
        NOTES: 32,
        EMAIL: 33,
        USERNAME: 34,
        COUNTRY_CODE: 36,
        EXTERNALSYSTEMREF: 37,
        CHANNEL: 38,
        CUSTOMERTYPE: 39,
        MEMBERSHIP: 40,
        ADDRESS_PHONE_DEFAULT: 41,
        PHONE_DEFAULT: 42,
        RIGHTHANDED: 43,
        OPT_IN_PROMOTIONS: 44,
        OPT_IN_PROMOTIONS_MODE: 45,
        OPT_OUT_WHATSAPP_MESSAGE: 46,
        EMAIL_DEFAULT: 47,
        ADDRESS_DEFAULT: 48,
        VERIFIED: 49,
        TERMS_AND_CONDITIONS: 1000,
    },
    BIRTH_DATE: 'BIRTH_DATE',
    DATE_FORMAT: 'dd-MM-yyyy',
    TERMS_AND_CONDITIONS: 'TERMS_AND_CONDITIONS',
    OPT_IN_PROMOTIONS: 'OPT_IN_PROMOTIONS',
    OPT_IN_PROMOTIONS_MODE: 'OPT_IN_PROMOTIONS_MODE',
    COUNTRY: 'COUNTRY',
    STATE: 'STATE',
    CUSTOMER_RELATIONSHIP_TYPE_ID: 1,
    EMAIL: 'EMAIL',
    PHONE_DEFAULT: 'CONTACT_PHONE',
    CONTACT_TYPE_EMAIL: 2,
    CONTACT_TYPE_PRIMARY: 1,
    CONTACT_TYPE_SECONDARY: 2,
    NUMERIC_PATTERN_REGEX: /^[0-9]+$/,
    EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[a-zA-Z]{2,}$/,
} as const;

/**
 * ================================================
 * USAGE
 * ================================================
 * use this utility function to get constants
 *
 * Ex:
 * getConstantValue();
 * getConstantValue('SAMPLE');
 * getConstantValue('API_ENDPOINTS.AUTHENTICATE_SYSTEM_USERS');
 * ================================================
 */
