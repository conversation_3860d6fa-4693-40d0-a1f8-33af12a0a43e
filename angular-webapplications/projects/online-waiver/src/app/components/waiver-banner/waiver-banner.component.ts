import { Component, signal } from '@angular/core';
import { ModalComponent } from 'lib-ui-kit';
import { HowToSignWaiverComponent } from "../how-to-sign-waiver/how-to-sign-waiver.component";
import { PreviewWaiverComponent } from "../preview-waiver/preview-waiver.component";

@Component({
  selector: 'app-waiver-banner',
  imports: [ModalComponent, HowToSignWaiverComponent, PreviewWaiverComponent],
  templateUrl: './waiver-banner.component.html',
  styleUrl: './waiver-banner.component.scss'
})
export class WaiverBannerComponent {
    readonly showHowToSignWaivers = signal<boolean>(false);
    readonly showPreviewWaiver = signal<boolean>(false);

    closeHowToSignWaivers() {
        this.showHowToSignWaivers.set(false);
    }

    closePreviewWaiver() {
        this.showPreviewWaiver.set(false);
    }
}
