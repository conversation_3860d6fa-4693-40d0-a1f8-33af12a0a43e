<div class="bg-surface-white rounded-4xl shadow-lg p-4 md:p-6 mb-6">
    <div class="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
            <h1 class="text-xl font-semibold text-primary">Sign waivers</h1>
            <p class="text-sm text-primary mt-1">
                Sign waivers to get access to the services.
            </p>
        </div>
        <div class="flex items-center gap-4 mt-4 md:mt-0">
            <button (click)="showHowToSignWaivers.set(true)"
                class="flex items-center gap-1 text-sm text-secondary-blue underline">
                <img src="assets/icons/close.svg" alt="Image" />
                <span class="text-left"> How to sign waivers?</span>
            </button>
            <button (click)="showPreviewWaiver.set(true)"
                class="flex items-center gap-1 text-sm text-secondary-blue underline">
                <img src="assets/icons/preview-green.svg" alt="Image" />
                <span class="text-left">Preview waiver</span>
            </button>
        </div>
    </div>
</div>



<ng-template #howToSignWaiversContent> <app-how-to-sign-waiver /> </ng-template>

<lib-modal [isOpen]="showHowToSignWaivers()" [modalContent]="howToSignWaiversContent"
    (closeModal)="closeHowToSignWaivers()" dialogueHeader="How to sign waivers?">
</lib-modal>

<ng-template #showPreviewWaiverContent>
    <app-preview-waiver />
</ng-template>

<lib-modal [isOpen]="showPreviewWaiver()" [modalContent]="showPreviewWaiverContent"
    (closeModal)="closePreviewWaiver()" dialogueHeader="Preview waiver">
</lib-modal>