/**
 * @fileoverview Reservation component for handling party reservation waivers
 * @description This component manages the reservation flow by validating reservation codes,
 *              processing waiver data, and handling the complete reservation lifecycle.
 *              It acts as the main orchestrator for reservation-related operations.
 * <AUTHOR> G
 * @version 1.0.0
 * @created 2025-08-14
 */

// Angular core imports for component functionality
import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { EMPTY, map, switchMap, catchError, Observable, tap } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { LibAuthPasswordResetBL } from 'lib/lib-auth/src/lib/business-layer/lib-auth-password-reset-bl.service';
import { waiverErrorMessages } from 'lib/lib-app-core/src/lib/constants/form-validation-message';
import { IPartyReservationQueryParams } from '../../interface/reservation.interface';
import { WaiverDetailsServiceBL } from '../../services/business-layer/waiver-details-bl.service';
import { LibUserDetailDL } from 'lib/lib-auth/src/lib/data-layer/lib-auth-user-details-dl.service';
import { TransactionDetailsDL } from 'lib-app-core';
import { WaiverSetDL } from '../../services/data-layer/waiver-set-dl.service';
import { SiteViewsServiceBL } from 'lib/lib-app-core/src/lib/services/business-layer/site-views-bl.service';
import { TransactionTimeDL } from 'lib-app-core';
import { SiteViewsServiceDL } from 'lib-app-core';
import { CreateDefaultCustomerDL } from '../../services/data-layer/create-default-customer-dl.service';
import { GetHtmlWaiverDL } from '../../services/data-layer/get-html-waiver-dl.service';
import { ValidateTokenResponse } from 'lib/lib-auth/src/lib/interfaces/reset-password.interface';
import { SkeletonLoaderComponent } from 'lib/lib-ui-kit/src/lib/components/skeleton-loader/skeleton-loader.component';
import { LibAuthPasswordResetDL } from 'lib/lib-auth/src/lib/data-layer/lib-auth-password-reset-dl.service';
import { LibAuthSecurityTokenDL } from 'lib/lib-auth/src/lib/data-layer/lib-auth-security-token.service';

/**
 * Reservation Component
 *
 * This component handles the complete reservation flow for party reservations:
 * 1. Validates reservation codes from URL parameters
 * 2. Authenticates tokens using the auth service
 * 3. Retrieves transaction data based on GUID
 * 4. Processes waiver information and site validation
 * 5. Manages error handling and navigation
 *
 * The component uses reactive programming with RxJS observables to handle
 * the asynchronous nature of API calls and data processing.
 */
@Component({
    selector: 'app-reservation',
    imports: [CommonModule, SkeletonLoaderComponent],
    templateUrl: './reservation.component.html',
    // Dependency injection providers for all required services
    providers: [
        // Authentication services
        LibAuthPasswordResetBL,
        LibUserDetailDL,
        LibAuthSecurityTokenDL,
        // Data layer services for various operations
        TransactionDetailsDL,
        WaiverSetDL,
        TransactionTimeDL,
        // Business logic services
        SiteViewsServiceBL,
        SiteViewsServiceDL,
        CreateDefaultCustomerDL,
        GetHtmlWaiverDL,
        // Additional auth services
        LibAuthPasswordResetDL,
        LibAuthSecurityTokenDL,
    ],
})
export class ReservationComponent {
    // Dependency injection using Angular's inject function
    private _route = inject(ActivatedRoute); // For accessing URL parameters
    private _validateBL = inject(LibAuthPasswordResetBL); // For token validation
    private _waiverDetailsBL = inject(WaiverDetailsServiceBL); // For waiver data processing
    private _router = inject(Router); // For navigation

    /**
     * Main observable stream that handles the complete reservation flow
     *
     * This observable chain performs the following operations:
     * 1. Extracts and validates reservation code from URL parameters
     * 2. Validates the password token using the auth service
     * 3. Extracts GUID from the validation response
     * 4. Retrieves transaction data using the GUID
     * 5. Processes waiver information and validates site ID
     * 6. Stores waiver info for later use
     * 7. Handles any errors by redirecting to registration page
     *
     */
    waiverSetData$ = this._route.queryParams.pipe(
        // Step 1: Extract and validate reservation code from URL parameters
        map((params: IPartyReservationQueryParams) =>
            this.validateParams(params)
        ),
        // Step 2: Validate the password token using the auth service
        switchMap((token) => {
            return this._validateBL.validatePasswordToken(token);
        }),
        // Step 3: Extract GUID from the validation response
        map((response) => {
            return this.extractGuidId(response);
        }),
        // Step 4: Retrieve transaction data using the extracted GUID
        switchMap((guidId) => this._waiverDetailsBL.getTransactionData(guidId)),
        // Step 5: Extract waiver information from transaction data
        map((response) => this._waiverDetailsBL.extractWaiverInfo(response)),
        // Step 6: Validate that the site ID matches expected values
        map((waiverInfo) => this._waiverDetailsBL.checkSiteIdMatch(waiverInfo)),
        // Step 7: Store waiver info for use in other components
        tap((waiverInfo) => {
            this._waiverDetailsBL.setWaiverInfo(waiverInfo);
        }),
        // Step 8: Handle any errors by redirecting to registration
        catchError(() => {
            return this.handleError();
        })
    );

    /**
     * Validates reservation parameters from URL query string
     *
     * @param params - Query parameters containing reservation information
     * @returns The validated reservation code
     * @throws Error if reservation code is missing or invalid
     */
    protected validateParams(params: IPartyReservationQueryParams): string {
        if (!params?.reservationCode) {
            throw new Error(waiverErrorMessages.noReservationCode);
        }
        return params.reservationCode;
    }

    /**
     * Extracts GUID from token validation response
     *
     * @param response - Response from token validation service
     * @returns The extracted GUID string
     * @throws Error if GUID is missing from response
     */
    protected extractGuidId(response: ValidateTokenResponse): string {
        if (!response?.data?.ObjectGuid) {
            throw new Error(waiverErrorMessages.noGuidId);
        }
        return response.data.ObjectGuid;
    }

    /**
     * Handles errors in the reservation flow
     *
     * This method is called when any step in the reservation process fails.
     * It redirects the user to the registration page and returns an empty observable
     * to prevent further processing.
     *
     * @returns Empty observable to terminate the stream
     */
    private handleError() {
        this._router.navigate(['/auth/register']);
        return EMPTY;
    }
}
