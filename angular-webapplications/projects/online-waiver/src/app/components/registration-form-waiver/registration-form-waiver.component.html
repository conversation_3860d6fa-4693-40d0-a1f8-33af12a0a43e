@if(customerUIMetadataServiceBL.isUIDataLoading()) {
<lib-skeleton-loader
    [count]="12"
    wrapperClass="grid grid-cols-1 md:grid-cols-3 gap-6"
    skeletonClass="animate-pulse rounded-xl h-10 bg-surface-lightest"
/>

} @else {
<lib-registration-form-base
    [fields]="dynamicMajorFields()"
    [fieldDescription]="fieldDescription"
    [errorMessages]="errorMessages"
    [registerForm]="registerForm"
    [isSubmitting]="customerUIMetadataServiceBL.isSubmitting()"
    (submit)="onSubmit($event)"
    (termsAccepted)="onTermsAccepted($event)"
>
    <div add-minor-form>
        @for (minorForm of minorForms.controls; track $index) {
        <app-add-minor-form
            [fields]="dynamicMinorFields()"
            [minorForm]="minorForm"
            [errorMessages]="errorMessages"
            [index]="$index"
            (remove)="removeMinorForm($index)"
        ></app-add-minor-form>
        }

        <!-- Add Minor Button -->
        <button
            type="button"
            (click)="addMinorForm()"
            class="w-full md:max-w-[300px] py-2 px-4 mb-5 md:mb-6 bg-surface-white border border-primary rounded-full text-primary font-medium flex items-center justify-center gap-2"
        >
            <img src="assets/icons/person-plus.svg" alt="Image" />
            <span i18n="registration-form-waiver.add-minor">Add Minor</span>
        </button>
    </div>
</lib-registration-form-base>

@if(customerUIMetadataServiceBL.errorMessage()) {
<p i18n="registration-form-waiver.error-message" class="text-red-500 mt-4 text-sm">
    {{ customerUIMetadataServiceBL.errorMessage() }}
</p>
} }
