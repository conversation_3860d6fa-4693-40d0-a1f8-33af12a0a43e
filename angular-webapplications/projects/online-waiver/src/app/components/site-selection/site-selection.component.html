<div
    class="max-w-[682px] mx-auto bg-surface-white rounded-4xl p-5 md:p-6 flex flex-col gap-5 md:gap-6"
>
    <h1 i18n="site-selection.title" class="font-semibold text-lg">Select your preferred location</h1>

    <lib-text-input
        placeholder="Search"
        [icon]="
            searchTerm()
                ? 'assets/icons/close-black.svg'
                : 'assets/icons/magnifier.svg'
        "
        [(ngModel)]="searchTerm"
        [onIconClick]="clearSearchTerm()"
    />

    <div class="grid gap-5 md:gap-6 place-items-center max-h-[40vh] overflow-y-auto px-2" [class]="customClass">
        @for (site of filteredSites(); track $index) {
        <button
            [tabIndex]="0"
            class="rounded-3xl bg-surface-lightest p-2 flex items-center gap-5 shadow-lg transition-colors w-full md:min-w-[305px]"
            [class]="siteViewsServiceBL.selectedSiteId() ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'"
            [disabled]="siteViewsServiceBL.selectedSiteId()"
            (click)="siteViewsServiceBL.onSiteSelect(site.SiteId)"
        >
            <img
                libImagePlaceholder
                [src]="site.Logo"
                [alt]="site.SiteName"
                class="object-cover rounded-3xl h-[90px] w-[90px]"
            />
            <div class="flex flex-col gap-1 text-left overflow-hidden">
                @if (siteViewsServiceBL.selectedSiteId() === site.SiteId) {
                    <div class="flex items-center gap-2">
                        <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                        <p i18n="site-selection.updating-site" class="font-medium text-sm">Updating site...</p>
                    </div>
                } @else {
                    <p i18n="site-selection.site-name" class="font-medium text-sm line-clamp-1 text-ellipsis">
                        {{ site.SiteName }}
                    </p>
                    <p i18n="site-selection.site-address" class="text-neutral-dark text-xs line-clamp-2">
                        {{ site.SiteAddress }}
                    </p>
                }
            </div>
        </button>

        } @empty {
        <div class="grid gap-5 md:gap-6 place-items-center">
            <img src="assets/icons/magnifier-L.svg" alt="No results" />

            <p i18n="site-selection.no-results-title" class="font-medium text-lg">No results found</p>

            <p i18n="site-selection.no-results-description" class="text-neutral-dark text-sm text-center px-8">
                The location you entered is invalid, incorrect, or currently
                outside our service area. Please try again.
            </p>
        </div>
        }
    </div>
</div>
