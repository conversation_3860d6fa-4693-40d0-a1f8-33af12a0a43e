/**
 * @fileoverview Site Selection Component
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-07-15
 */

import { CommonModule } from '@angular/common';
import { Component, computed, inject, signal, ChangeDetectionStrategy, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { SiteViewsServiceDL } from 'lib-app-core';
import { TextInputComponent, ImagePlaceholderDirective } from 'lib-ui-kit';
import { SiteViewsServiceBL } from 'lib/lib-app-core/src/lib/services/business-layer/site-views-bl.service';

@Component({
    selector: 'app-site-selection',
    standalone: true,
    imports: [TextInputComponent, FormsModule, CommonModule, ImagePlaceholderDirective],
    providers: [SiteViewsServiceDL, SiteViewsServiceBL],
    templateUrl: './site-selection.component.html',
    styleUrl: './site-selection.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SiteSelectionComponent implements OnInit {
    readonly siteViewsServiceBL = inject(SiteViewsServiceBL);  
    readonly searchTerm = signal('');
    
    readonly filteredSites = computed(() => {
        const term = this.searchTerm().toLowerCase().trim();
        return this.siteViewsServiceBL.filterSiteViews(term);
    });

    /**
     * If search term is not empty, return a function that sets the search term to empty
     */
    readonly clearSearchTerm = computed(() =>
        this.searchTerm() ? () => this.searchTerm.set('') : null
    );

    get customClass() {
        return this.filteredSites().length > 0
            ? 'grid-cols-1 md:grid-cols-2'
            : 'grid-cols-1';
    }

    ngOnInit(): void {
        this.siteViewsServiceBL.initializeSiteViews();
    }
}
