<section class="grid gap-5 md:gap-6">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        @for (participant of participants(); track $index) {
        <div class="bg-surface-white rounded-3xl shadow-lg p-6 w-full">
            <div class="text-xs text-neutral-dark">
                <span i18n="relations-item.participant-type">{{ participant.type === 'primary' ? 'Primary Account Holder' : 'Minor' }}</span>
            </div>
            <div class="text-primary font-medium text-sm">
                <span i18n="relations-item.participant-name">{{ participant.firstName }} {{ participant.lastName }}</span>
            </div>
        </div>
        }
    </div>
</section>
