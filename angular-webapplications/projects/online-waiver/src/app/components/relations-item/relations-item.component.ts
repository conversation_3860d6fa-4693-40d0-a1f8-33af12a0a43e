/**
 * @fileoverview RelationsItemComponent is a component that displays individual relation/participant items
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-08-12
 */
/**
 * @component RelationsItemComponent
 * @description
 * Displays individual customer relation/participant items in a list format with participant information
 * and actions. This component is used to render each participant in the relations list.
 *
 * @usage
 * This component is used within the my-relations page to display individual participant items
 *
 * @inputs
 * - participants: Array of IParticipant objects containing participant information
 *
 * @dependencies
 * - CommonModule: Provides common Angular directives
 * - IParticipant: Interface defining participant data structure
 *
 * @methods
 * - No methods defined in this component
 */

import { Component, input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IParticipant } from '../../interface/customer.interface';

/**
 * Component for displaying individual relation/participant items
 */
@Component({
    selector: 'app-relations-item',
    standalone: true,
    imports: [CommonModule],
    templateUrl: './relations-item.component.html',
    styleUrl: './relations-item.component.scss',
})
export class RelationsItemComponent {
    participants = input.required<IParticipant[]>();
}
