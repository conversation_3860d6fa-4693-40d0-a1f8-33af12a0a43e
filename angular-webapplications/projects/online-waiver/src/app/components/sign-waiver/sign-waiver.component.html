<div class="flex flex-col gap-y-4 lg:gap-6 md:min-h-[calc(100vh-168px)]">
    <!-- Page Header -->
    <h1 i18n="sign-waiver.title" class="text-xl md:text-lg font-semibold text-gray-900">
        Sign waivers - {{ waiverName() }}
    </h1>

    <app-participant-card></app-participant-card>


    <div class="w-full">
        <div class="bg-white rounded-4xl shadow-sm border border-gray-100 py-4">
            <div class="pb-2 md:pb-4 px-4">
                <p i18n="sign-waiver.participants" class="text-lg font-medium text-black-900">Read and sign waiver</p>
                <p i18n="sign-waiver.participants-list" class="text-sm text-gray-600">Please read the waiver carefully
                    and sign at the end of the document.
                </p>
            </div>

            <!-- Single Waiver Content -->
            @if (waiverData()) {
            <div class="px-4 py-4">
                <!-- Waiver Header -->
                <div class="flex items-center justify-between mb-4">
                    <span class="font-bold text-gray-900 text-lg">
                        {{ waiverName() }}
                    </span>
                    <div class="flex items-center space-x-3 md:mr-12">
                        @if (isWaiverSigned()) {
                        <div
                            class="inline-flex items-center gap-2 px-4 py-2 bg-green-100 border border-green-100 rounded-3xl">
                            <img src="assets/icons/green-tick.svg" alt="tick-circle" class="w-6 h-6">
                            <span class="text-sm text-green-600 font-medium"
                                i18n="sign-waiver.signed-status">Signed</span>
                        </div>
                        } @else {
                        <div
                            class="inline-flex items-center gap-2 px-4 py-2 bg-red-100 border border-red-100 rounded-3xl">
                            <img src="assets/icons/close-red.svg" alt="cross-circle" class="w-6 h-6">
                            <span class="text-sm text-red-700 font-medium" i18n="sign-waiver.not-signed">Not
                                signed</span>
                        </div>
                        }
                    </div>
                </div>

                <!-- Waiver Preview -->
                <app-preview-waiver [waiverData]="waiverData()" />

                <!-- Dynamic Button -->
                <div class="mt-4">
                    <ng-container [ngTemplateOutlet]="signCTA"></ng-container>
                </div>
            </div>
            }

        </div>
    </div>
</div>

<!-- Sign CTA Template -->
<ng-template #signCTA>
    <div class="flex flex-col items-center md:items-start">
        @if (waiverData()?.type === 'pdf') {
        @if (!isWaiverSigned()) {
        <lib-button type="primary" size="lg" [fullWidth]="true" (clicked)="showSignModal(0)">
            Sign Waiver
        </lib-button>
        } @else {
        <lib-button type="primary" size="lg" [fullWidth]="true" (clicked)="proceedToNext()">
            Check In
        </lib-button>
        }
        } @else {
        <lib-button type="primary" size="lg" [fullWidth]="true" (clicked)="proceedToNext()">
            Check In
        </lib-button>
        }
    </div>
</ng-template>

<!-- Signature Modal -->
<!-- <ng-template #signatureModal>
    <div class="px-6 pb-6">
        <app-waiver-signature (signatureData)="onSignatureSubmitted($event)">
        </app-waiver-signature>
    </div>
</ng-template> -->

<!-- <lib-modal [isOpen]="showSignatureModal()" [modalContent]="signatureModal" (closeModal)="closeSignatureModal()"
    [dialogueHeader]="getSelectedWaiverName()">
</lib-modal> -->