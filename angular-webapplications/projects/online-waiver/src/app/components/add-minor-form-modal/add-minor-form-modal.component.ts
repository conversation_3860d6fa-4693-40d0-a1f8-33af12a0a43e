import {
    Component,
    effect,
    inject,
    Injector,
    input,
    OnInit,
    output,
    runInInjectionContext,
    signal
} from '@angular/core';
import {
    FormArray,
    FormBuilder,
    FormGroup,
    ReactiveFormsModule,
} from '@angular/forms';
import {
    CountryContainerServiceDL,
    CreateCustomerServiceDL,
    CustomerUIMetadataContainerDTOModel,
    CustomerUIMetadataServiceDL,
    formValidationMessages,
} from 'lib-app-core';
import { LibAuthLoginBL, LibAuthLoginDL } from 'lib-auth';
import { DynamicFormField, FormValidatorService } from 'lib-ui-kit';
import { DynamicFormComponent } from '../dynamic-form/dynamic-form.component';
import { CreateCustomerPayloadBuilderService } from '../../services/buisiness-layer/create-customer-payload-builder.service';
import {
    CustomerUIMetadataServiceBL
} from '../../services/buisiness-layer/customer-ui-metadata-bl.service';

type ErrorMessage = {
    [field: string]: Record<string, string | undefined>;
};

@Component({
    selector: 'app-add-minor-form-modal',
    imports: [ReactiveFormsModule, DynamicFormComponent],
    providers: [
        CustomerUIMetadataServiceDL,
        CustomerUIMetadataServiceBL,
        CountryContainerServiceDL,
        CreateCustomerServiceDL,
        CreateCustomerPayloadBuilderService,
        FormValidatorService,
        LibAuthLoginBL,
        LibAuthLoginDL,
    ],
    templateUrl: './add-minor-form-modal.component.html',
    styleUrl: './add-minor-form-modal.component.scss',
})
export class AddMinorFormModalComponent implements OnInit {
    loading = input<boolean>(false);
    errorMessages: ErrorMessage = {};
    submit = output<[FormArray<FormGroup>, CustomerUIMetadataContainerDTOModel[]]>();
    minorForms = signal<FormArray<FormGroup>>(new FormArray<FormGroup>([]));
   
    private readonly _injector = inject(Injector);
    private readonly _customerUIMetadataServiceBL = inject(CustomerUIMetadataServiceBL);
    private readonly _fb = inject(FormBuilder);
    readonly dynamicMinorFields = signal<DynamicFormField[]>([]);

    ngOnInit(): void {
        this.setupFields();
    }

    private setupFields(): void {
        runInInjectionContext(this._injector, () => {
            this._customerUIMetadataServiceBL.loadInitialData()
            effect(() => {
                const minorFields =
                    this._customerUIMetadataServiceBL.minorFormFields();

                this.dynamicMinorFields.set(minorFields);

                // Generate error messages once fields are ready
                this.errorMessages = {
                    ...formValidationMessages,
                    ...this._customerUIMetadataServiceBL.generateErrorMessages(
                        minorFields
                    ),
                };

                // Automatically add a minor form when fields become available
                if (minorFields.length > 0 && this.minorForms().length === 0) {
                    this.addMinorForm();
                }
            });
        });
    }

    onSubmit(): void {
        if (this.minorForms().valid) {
            this.submit.emit([this.minorForms(), this._customerUIMetadataServiceBL.customerUIMetadata()]);
        }
    }

    addMinorForm(): void {
        const minorFields = this.dynamicMinorFields();
        if (minorFields.length > 0) {
            const newFormGroup =
                this._customerUIMetadataServiceBL.createFormGroupFromFields(
                    minorFields,
                    this._fb
                );
            this.minorForms().push(newFormGroup);
        }
    }

    removeMinorForm(index: number): void {
        // Remove the form group at the specified index
        this.minorForms().removeAt(index);
    }

    private markFormGroupTouched(formGroup: FormGroup): void {
        Object.keys(formGroup.controls).forEach((key) => {
            const control = formGroup.get(key);
            if (control) {
                control.markAsTouched();
                if (control instanceof FormGroup) {
                    this.markFormGroupTouched(control);
                }
            }
        });
    }

    get disabled(): boolean {
        return this.minorForms().invalid || this.loading() || this.minorForms().controls.length === 0;
    }
}
