<div
    class="p-5 md:p-6 pt-0 md:pt-0 w-[90vw] md:max-w-[70vw] h-[70vh] overflow-y-auto flex flex-col gap-4 md:gap-6"
>
    @for (minorForm of minorForms().controls; track $index) {
    <div class="p-5 shadow-lg rounded-3xl flex flex-col gap-4">
        <div class="flex items-center justify-between gap-0.5">
            <h1 i18n="add-minor-form-modal.participant" class="text-lg font-medium">Participant {{ $index + 1 }}</h1>
            <button
                type="button"
                class="flex items-center justify-center gap-0.5 text-secondary-blue underline"
                (click)="removeMinorForm($index)"
            >
                <img src="/assets/icons/remove.svg" alt="Remove" />
            </button>
        </div>
        <form [formGroup]="minorForm">
            <lib-dynamic-form
                [fields]="dynamicMinorFields()"
                [errorMessages]="errorMessages"
                [form]="minorForm"
            ></lib-dynamic-form>
        </form>
    </div>
    } @empty {
    <div
        class="flex flex-col items-center justify-center h-full gap-4 md:gap-6"
    >
        <p i18n="add-minor-form-modal.no-participants-added" class="text-center text-lg text-gray-500">   
            No participants added yet
        </p>
    </div>
    }
    <button
        type="button"
        class="flex items-center justify-center gap-0.5 text-secondary-blue underline self-start"
        (click)="addMinorForm()"
    >
        <img src="/assets/icons/person-plus-blue.svg" alt="Add one more" />
        <span i18n="add-minor-form-modal.add-one-more">Add one more</span>
    </button>
    <hr class="text-surface hidden md:block" />
    <button
        type="submit"
        [disabled]="disabled"
        (click)="onSubmit()"
        class="w-full py-[13px] px-4 bg-primary text-white font-medium rounded-4xl disabled:bg-surface md:max-w-[300px] md:self-start"
    >
        <span i18n="add-minor-form-modal.proceed">Proceed</span>
    </button>
</div>
