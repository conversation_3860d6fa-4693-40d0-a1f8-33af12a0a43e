<div class="mx-auto max-w-7xl mb-32 md:mb-0">
    <!-- Party Card Header -->
    <app-preview-waiver-card />

    <!-- Tabs and Forms -->
    <div>
        <!-- Tabs -->
        <lib-tabs [tabs]="authTabs" [activeTabId]="activeTabId()" variant="rounded" (tabChange)="onTabChange($event)">
        </lib-tabs>

        <div class="bg-surface-lightest rounded-b-4xl md:rounded-tr-4xl shadow-lg">
            @if (router.url === '/auth/login') {
            <div class="bg-surface-white rounded-b-4xl md:rounded-tr-4xl overflow-hidden rounded-tl-4xl">
                <!-- Form Container -->
                <div class="p-5 md:p-6 shadow-md">
                    <router-outlet></router-outlet>
                </div>
            </div>
            } @else if (router.url === '/auth/register') {
            <div class="bg-surface-white rounded-b-4xl md:rounded-tr-4xl overflow-hidden rounded-tr-4xl">
                <!-- Form Container -->
                <div class="p-5 md:p-6 shadow-md">
                    <router-outlet></router-outlet>
                </div>
            </div>
            } @else {
            <div class="bg-surface-white rounded-b-4xl md:rounded-tr-4xl overflow-hidden">
                <!-- Form Container -->
                <div class="p-5 md:p-6 shadow-md">
                    <router-outlet></router-outlet>
                </div>
            </div>
            }
        </div>
    </div>
</div>