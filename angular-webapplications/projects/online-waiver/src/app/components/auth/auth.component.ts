/**
 * @fileoverview Auth component
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-07-17
 */

import { Component, inject, signal } from '@angular/core';
import {
    ActivatedRoute,
    Router,
    RouterLinkActive,
    RouterModule,
} from '@angular/router';
import { CommonModule } from '@angular/common';
import { PreviewWaiverCardComponent } from '../preview-waiver-card/preview-waiver-card.component';
import { TabsComponent, TabItem } from 'lib-ui-kit';
import { CreateDefaultCustomerDL } from '../../services/data-layer/create-default-customer-dl.service';
import { TransactionDetailsDL } from 'lib-app-core';
import { WaiverSetDL } from '../../services/data-layer/waiver-set-dl.service';
import { TransactionTimeDL } from 'lib-app-core';
import { WaiverDetailsServiceBL } from '../../services/business-layer/waiver-details-bl.service';
import { GetHtmlWaiverDL } from '../../services/data-layer/get-html-waiver-dl.service';
import { CookieService } from 'lib/lib-app-core/src/lib/services/cookie-service/cookie.service';
import { SiteViewsServiceBL } from 'lib/lib-app-core/src/lib/services/business-layer/site-views-bl.service';

@Component({
    selector: 'app-auth',
    imports: [
        CommonModule,
        RouterModule,
        PreviewWaiverCardComponent,
        TabsComponent,
    ],
    templateUrl: './auth.component.html',
    styleUrl: './auth.component.scss',
    providers: [
        CreateDefaultCustomerDL,
        TransactionDetailsDL,
        WaiverSetDL,
        TransactionTimeDL,
        GetHtmlWaiverDL,
        CookieService,
    ],
})
export class AuthComponent {
    protected readonly router = inject(Router);

    protected readonly authTabs: TabItem[] = [
        {
            id: 'register',
            label: 'Register',
            subtitle: 'New customer?',
            route: '/auth/register',
        },
        {
            id: 'login',
            label: 'Login',
            subtitle: 'Existing customer?',
            route: '/auth/login',
        },
    ];

    protected readonly activeTabId = signal<string>('');

    ngOnInit() {
        // Set active tab based on current route
        const currentRoute = this.router.url;
        if (currentRoute.includes('/auth/register')) {
            this.activeTabId.set('register');
        } else if (currentRoute.includes('/auth/login')) {
            this.activeTabId.set('login');
        }
    }

    onTabChange(tabId: string) {
        this.activeTabId.set(tabId);
    }
}
