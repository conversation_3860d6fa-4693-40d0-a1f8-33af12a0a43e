<!-- Preview Waiver Card Component Template -->
<div class="preview-waiver-card mb-6">
    @if (partyCardState$ | async; as state) {
    @if (state.loading) {
    <!-- Loading State -->
    <section class="p-5 bg-surface-lightest rounded-3xl flex flex-col gap-2">
        <lib-skeleton-loader [count]="2" wrapperClass="flex flex-col gap-2"
            skeletonClass="h-4 bg-gray-200 rounded animate-pulse">
        </lib-skeleton-loader>
    </section>
    } @else if (state.error) {
    <!-- Error State -->
    <section class="p-5 bg-red-50 rounded-3xl flex flex-col gap-2">
        <div class="flex items-center gap-2 mb-2">
            <img src="assets/icons/warning.svg" alt="Error" class="w-6 h-6" />
            <p class="text-red-600 font-medium">
                Failed to load waiver data
            </p>
        </div>
        <p class="text-red-500 text-sm mb-3">
            {{ state.error }}
        </p>
    </section>
    } @else if (state.waiverSets) {
    <!-- Success State -->
    @if (state.isSingleParty) {
    <app-party-preview-waiver-card [waiverSets]="state.waiverSets">
    </app-party-preview-waiver-card>
    } @else {
    <app-default-preview-waiver-card></app-default-preview-waiver-card>
    }
    }
    }
</div>