@if (showTermsAndConditions()) {
<div class="flex gap-2 items-center text-sm">
    <lib-checkbox
        [id]="'terms-checkbox'"
        customClass="w-[18px] h-[18px] self-end"
        [(ngModel)]="checked"
    ></lib-checkbox>
    <label for="terms-checkbox">
        @if (termsAndConditionsBL.termsAndConditions()) {
        <!-- RichContents has high priority - show button that opens modal -->
            <span i18n="terms-and-conditions.i-agree-to-the-text">I agree to the </span>
            <button
                type="button"
                (click)="onTermsClick()"
                class="underline text-secondary-blue"
            >
                <span i18n="terms-and-conditions.terms-and-conditions-button" class="text-sm">
                    {{ termsAndConditionsBL.termsAndConditions()?.ContentName || "Terms and Conditions" }}
                </span>
            </button>
        } @else if (termsAndConditionsField()?.EntityFieldCaption) {
        <!-- CustomerUIMetadata - display with working links using [innerHTML] -->
        <span [innerHTML]="termsAndConditionsHTML()"></span>
        }
    </label>
</div>
} @else{
<lib-skeleton-loader
    skeletonClass="animate-pulse h-5 w-80 bg-surface-lightest rounded-md"
/>
}

<!-- Modal for displaying terms and conditions -->
<lib-modal
    [isOpen]="showModal()"
    [dialogueHeader]="modalTitle()"
    (closeModal)="onModalClose()"
    [modalContent]="pdfViewerContent"
>
</lib-modal>

<!-- PDF Viewer Modal content -->
<ng-template #pdfViewerContent>
    <div class="px-5 pb-5">
        @if (sanitizedPdfUrl()) {
        <iframe
            [src]="sanitizedPdfUrl()"
            title="PDF Viewer"
            loading="lazy"
            class="min-h-[70vh] md:min-h-[80vh] min-w-[50vw] object-contain flex justify-center"
        >
        </iframe>
        }@else{
        <p
            class="text-red-500 text-center min-w-[20vw] min-h-[20vh] flex items-center justify-center"
        >
            No PDF found, please contact support.
        </p>
        }
    </div>
</ng-template>
