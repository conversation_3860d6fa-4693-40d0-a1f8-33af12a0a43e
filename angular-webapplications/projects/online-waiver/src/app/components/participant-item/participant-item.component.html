<div class="flex items-center justify-between px-3 py-2 bg-surface-lightest rounded-[16px]">
    <div class="flex items-center justify-center gap-3">
        <lib-checkbox customClass="w-[18px] h-[18px]" [ngModel]="isSelected" (ngModelChange)="onCheckboxChange($event)" />
        <div class="flex flex-col">
            <p i18n="participant-item.name" class="text-sm">
                {{ participant().firstName + ' ' + participant().lastName }}
            </p>
            <div i18n="participant-item.date-of-birth" class="text-sm text-neutral-dark">
                {{ participant().dateOfBirth | date:'dd-MM-yyyy' }}
            </div>
        </div>
    </div>
</div>