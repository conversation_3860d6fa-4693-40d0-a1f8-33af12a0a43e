/**
 * @fileoverview ParticipantItemComponent is a component that displays a participant item
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-07-30
 */
import { CommonModule } from '@angular/common';
import { Component, input, output } from '@angular/core';
import { CheckBoxComponent } from 'lib-ui-kit';
import { IParticipant } from '../../interface/customer.interface';
import { FormsModule } from '@angular/forms';

@Component({
    selector: 'app-participant-item',
    standalone: true,
    imports: [CommonModule, CheckBoxComponent, FormsModule],
    templateUrl: './participant-item.component.html',
    styles: [],
})
export class ParticipantItemComponent {
    participant = input.required<IParticipant>();
    isSelected = input.required<boolean>();
    checkboxChange = output<{
        participant: IParticipant;
        checked: boolean;
    }>();

    onCheckboxChange(event: boolean): void {
        this.checkboxChange.emit({
            participant: this.participant(),
            checked: event,
        });
    }
}
