/**
 * @fileoverview Dynamic form component
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-08-05
*/
import { CommonModule } from '@angular/common';
import { Component, computed, input } from '@angular/core';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import {
    CheckBoxComponent,
    DateDropdownPickerComponent,
    DynamicFormField,
    ErrorMessage,
    PhoneInputComponent,
    SelectComponent,
    TextInputComponent,
} from 'lib-ui-kit';
import { waiverConstants } from '../../constants/waiver.constant';
import { Message } from '../../services/buisiness-layer/customer-ui-metadata-bl.service';



@Component({
    selector: 'lib-dynamic-form',
    imports: [
        ReactiveFormsModule,
        CommonModule,
        TextInputComponent,
        PhoneInputComponent,
        CheckBoxComponent,
        SelectComponent,
        DateDropdownPickerComponent,
    ],
    templateUrl: './dynamic-form.component.html',
    styleUrl: './dynamic-form.component.scss',
})
export class DynamicFormComponent {
    fields = input.required<DynamicFormField[]>();
    form = input.required<FormGroup>();
    errorMessages = input<ErrorMessage<Message>>();
    columns = input<number>(3);

    /**
     * Computed property to categorize fields into regular, checkbox, and optInPromotions
     * @returns Object containing categorized fields
     */
    readonly categorizedFields = computed(() => {
        const regularFields: DynamicFormField[] = [];
        const checkboxFields: DynamicFormField[] = [];

        for (const field of this.fields()) {
            const { type, fieldName } = field;
             if (type === 'checkbox' && fieldName !== waiverConstants.TERMS_AND_CONDITIONS && fieldName !== waiverConstants.OPT_IN_PROMOTIONS) {
                checkboxFields.push(field);
            } else {
                regularFields.push(field);
            }
        }

        return {
            regularFields,
            checkboxFields,
        };
    });
}
