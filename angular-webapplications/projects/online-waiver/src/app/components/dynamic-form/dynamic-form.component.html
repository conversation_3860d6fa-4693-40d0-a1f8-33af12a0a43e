<div [formGroup]="form()" class="grid gap-6 grid-cols-1 md:grid-cols-{{ columns() }}">
    @for (field of categorizedFields().regularFields; track $index) {
    <ng-container [ngSwitch]="field.type">
        <lib-text-input
            *ngSwitchCase="'text'"
            [id]="field.id"
            formControlName="{{ field.fieldName }}"
            [label]="field.label"
            [type]="'text'"
            [required]="field.required"
            [placeholder]="field.placeholder ?? ''"
            [errorMessages]="errorMessages()?.[field.fieldName]"
        ></lib-text-input>

        <lib-text-input
            [id]="field.id"
            formControlName="{{ field.fieldName }}"
            *ngSwitchCase="'number'"
            [label]="field.label"
            [type]="'number'"
            [required]="field.required"
            [placeholder]="field.placeholder ?? ''"
            [errorMessages]="errorMessages()?.[field.fieldName]"
        ></lib-text-input>

        <lib-text-input
            [id]="field.id"
            formControlName="{{ field.fieldName }}"
            *ngSwitchCase="'email'"
            [label]="field.label"
            [type]="'email'"
            [required]="field.required"
            [placeholder]="field.placeholder ?? ''"
            [errorMessages]="errorMessages()?.[field.fieldName]"
        ></lib-text-input>

        <lib-phone-input
            [id]="field.id"
            formControlName="{{ field.fieldName }}"
            *ngSwitchCase="'phone'"
            [label]="field.label"
            [required]="field.required"
            [placeholder]="field.placeholder ?? ''"
            [errorMessages]="errorMessages()?.[field.fieldName]"
            [countries]="field.countryCodeOptions ?? []"
            [defaultCountry]="field.countryCodeOptions?.[0] ?? { name: '', dialCode: '' }"
        ></lib-phone-input>

        <lib-select
            [id]="field.id"
            formControlName="{{ field.fieldName }}"
            *ngSwitchCase="'select'"
            [label]="field.label"
            [options]="field.options ?? []"
            [required]="field.required"
            [placeholder]="field.placeholder ?? ''"
            [errorMessages]="errorMessages()?.[field.fieldName]"
        ></lib-select>

        <lib-date-dropdown-picker
            [id]="field.id"
            formControlName="{{ field.fieldName }}"
            *ngSwitchCase="'date'"
            [label]="field.label"
            [required]="field.required"
            [placeholder]="field.placeholder ?? ''"
            [errorMessages]="errorMessages()?.[field.fieldName]"
            [maxYear]="field.maxYear"
            [minYear]="field.minYear"
        ></lib-date-dropdown-picker>
    </ng-container>
    }

    <div class="col-span-full">
        <div class="grid gap-6 grid-cols-1 md:grid-cols-{{ columns() }}">
            @for (field of categorizedFields().checkboxFields; track $index) {
            <lib-checkbox
                [id]="field.id"
                formControlName="{{ field.fieldName }}"
                [label]="field.label"
                [required]="field.required"
                [errorMessages]="errorMessages()?.[field.fieldName]"
                customClass="w-[18px] h-[18px]"
                class="self-end"
            ></lib-checkbox>
            }
        </div>
    </div>
</div>
