/**
 * @fileoverview Language selection dropdown component
 * <AUTHOR>
 * @created 2025-08-21
 */

import { Component, computed, inject, OnInit, signal } from '@angular/core';
import { LanguageContainerServiceBL } from 'lib/lib-app-core/src/lib/services/business-layer/language-container-bl.service';
import {
    DropdownItem,
    DropdownMenuComponent,
} from '../../shared/dropdown-menu/dropdown-menu.component';

@Component({
    selector: 'app-language-selection',
    imports: [DropdownMenuComponent],
    templateUrl: './language-selection.component.html',
    styleUrl: './language-selection.component.scss',
})
export class LanguageSelectionComponent implements OnInit {
    private readonly languageContainerServiceBL = inject(
        LanguageContainerServiceBL
    );

    readonly languageMenuItems = computed<DropdownItem[]>(() =>
        this.languageContainerServiceBL.languageContainer().map((language) => ({
            label: language.LanguageName,
            value: language.LanguageCode,
            action: () =>
                this.languageContainerServiceBL.selectLanguage(language),
        }))
    );

    readonly selectedLanguage = computed(
        () => this.languageContainerServiceBL.selectedLanguage()?.LanguageName
    );

    readonly defaultSelection = computed(
        () =>
            this.languageMenuItems().find(
                (item) => item.label === this.selectedLanguage()
            ) ?? null
    );

    ngOnInit() {
        this.languageContainerServiceBL.initializeLanguageContainer();
    }
}
