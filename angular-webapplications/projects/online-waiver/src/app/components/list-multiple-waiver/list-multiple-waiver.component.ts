/**
 * @fileoverview List Multiple Waiver Component
 * @description This component handles the display and management of multiple waiver scenarios
 *              in the online waiver system. It determines whether to show a single waiver
 *              or multiple waivers based on the available waiver sets, and provides
 *              navigation to detailed waiver views.
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-08-21
 */

// Angular core imports for component functionality
import { Component, input, signal, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { SkeletonLoaderComponent } from 'lib-ui-kit';
import { map, switchMap, tap } from 'rxjs';
import {
    WaiverPreviewData,
    WaiverSetContainerDTO,
    WaiverSetResponse,
} from '../../interface/waiver.interface';
import { PreviewWaiverComponent } from '../preview-waiver/preview-waiver.component';
import { WaiverDetailsServiceBL } from '../../services/business-layer/waiver-details-bl.service';
import { TransactionDetailsDL } from 'lib-app-core';
import { WaiverSetDL } from '../../services/data-layer/waiver-set-dl.service';
import { TransactionTimeDL } from 'lib-app-core';
import { SiteViewsServiceBL } from 'lib/lib-app-core/src/lib/services/business-layer/site-views-bl.service';
import { CreateDefaultCustomerDL } from '../../services/data-layer/create-default-customer-dl.service';
import { GetHtmlWaiverDL } from '../../services/data-layer/get-html-waiver-dl.service';

/**
 * ListMultipleWaiverComponent - Multiple Waiver Management Component
 *
 * This component serves as the entry point for handling multiple waiver scenarios.
 * It provides intelligent routing logic that automatically determines whether to:
 * - Display a single waiver directly (auto-navigation)
 * - Show a list of multiple waivers for user selection
 *
 * Key Features:
 * - Automatic single waiver detection and navigation
 * - Multiple waiver set filtering and processing
 * - Reactive data flow using RxJS observables
 * - Integration with waiver details business logic
 *
 * The component follows a reactive programming approach to ensure
 * smooth user experience and efficient data handling.
 */
@Component({
    selector: 'app-list-multiple-waiver',
    standalone: true,
    imports: [CommonModule, SkeletonLoaderComponent],
    templateUrl: './list-multiple-waiver.component.html',
    providers: [
        TransactionDetailsDL,
        WaiverSetDL,
        TransactionTimeDL,
        SiteViewsServiceBL,
        CreateDefaultCustomerDL,
        GetHtmlWaiverDL,
    ],
})
export class ListMultipleWaiverComponent {
    readonly _waiverDetailsBL = inject(WaiverDetailsServiceBL);
    readonly router = inject(Router);

    /**
     * Main Waiver Set Observable - Waiver set data for list display
     *
     * This observable stream handles the complete lifecycle of waiver set processing:
     * 1. Listens to waiver info changes from the business logic service
     * 2. Fetches waiver sets from the API
     * 3. Extracts and filters waiver sets by waiver set ID
     * 4. Applies effective date filtering (active waivers only)
     * 5. Automatically navigates to single waiver if only one exists
     *
     * Auto-navigation Logic:
     * - If only one waiver set is available, automatically navigate to detailed view
     * - If multiple waiver sets exist, display the list for user selection
     * - This provides a seamless user experience for single waiver scenarios
     */
    waiverSet$ = this._waiverDetailsBL.waiverInfo$.pipe(
        // Step 1: Fetch waiver sets when waiver info changes
        switchMap(() => this._waiverDetailsBL.getWaiverSet()),
        // Step 2: Extract waiver sets from API response
        map((response: WaiverSetResponse) =>
            this._waiverDetailsBL.getAllWaiverSetsByWaiverSetId(response)
        ),
        // Step 3: Filter waiver sets by effective dates (active waivers only)
        map((waiverSets: WaiverSetContainerDTO[]) =>
            this._waiverDetailsBL.filterWaiverSetsByEffectiveDates(waiverSets)
        ),
        // Step 4: Auto-navigate to single waiver if only one exists
        tap((waiverSets) => {
            if (waiverSets.length === 1) {
                this.navigateToDetailedWaiver(waiverSets[0]);
            }
        })
    );

    /**
     * Handles waiver selection from the list
     *
     * This method is called when a user selects a specific waiver from
     * the multiple waiver list. It navigates to the detailed waiver view
     * for the selected waiver set.
     *
     * @param waiverSet - The selected waiver set object
     */
    selectWaiver(waiverSet: WaiverSetContainerDTO): void {
        this.navigateToDetailedWaiver(waiverSet);
    }

    /**
     * Navigates to the detailed waiver view
     *
     * This method constructs the navigation URL and redirects the user
     * to the detailed waiver view for the specified waiver set.
     * The URL includes the waiver set ID for proper routing and data loading.
     *
     * @param waiverSet - The waiver set to navigate to
     */
    navigateToDetailedWaiver(waiverSet: WaiverSetContainerDTO): void {
        this.router.navigate(['/waivers/sign-waiver', waiverSet.WaiverSetId]);
    }
}
