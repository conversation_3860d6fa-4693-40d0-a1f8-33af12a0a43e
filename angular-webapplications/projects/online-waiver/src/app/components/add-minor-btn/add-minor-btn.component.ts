/**
 * @fileoverview Add Minor Button Component
 * <AUTHOR>
 * @created 2025-08-14
 * @version 1.0.0
 */
import { CommonModule } from '@angular/common';
import {
    Component,
    effect,
    inject,
    input,
    OnInit,
    output,
} from '@angular/core';
import { FormArray, FormGroup } from '@angular/forms';
import {
    AddMinorServiceDL,
    CustomerUIMetadataContainerDTOModel,
} from 'lib-app-core';
import { ModalComponent } from 'lib-ui-kit';
import { IParticipant } from '../../interface/customer.interface';
import { AddMinorFormModalComponent } from '../add-minor-form-modal/add-minor-form-modal.component';
import { AddMinorServiceBL } from '../../services/buisiness-layer/add-minor-bl.service';
import { CreateCustomerPayloadBuilderService } from '../../services/buisiness-layer/create-customer-payload-builder.service';

@Component({
    selector: 'app-add-minor-btn',
    imports: [CommonModule, AddMinorFormModalComponent, ModalComponent],
    providers: [
        CreateCustomerPayloadBuilderService,
        AddMinorServiceDL,
        AddMinorServiceBL,
    ],
    templateUrl: './add-minor-btn.component.html',
    styleUrl: './add-minor-btn.component.scss',
})
export class AddMinorBtnComponent implements OnInit {
    variant = input<'primary' | 'link' | 'outline'>('primary');
    label = input<string>('Add Minor');
    primaryAccountData = input.required<IParticipant>();
    addMinorSuccess = output<boolean>();
    protected readonly addMinorServiceBL = inject(AddMinorServiceBL);

    constructor() {
        effect(() => {
            const success = this.addMinorServiceBL.addMinorSuccess();
            if (success) {
                this.addMinorSuccess.emit(true);
                // Reset the signal after emitting to allow future emissions
                this.addMinorServiceBL.addMinorSuccess.set(false);
            }
        });
    }

    ngOnInit(): void {}

    get customClasses(): string {
        switch (this.variant()) {
            case 'primary':
                return 'bg-primary text-white rounded-4xl py-[13px] px-4 flex items-center justify-center gap-2';
            case 'outline':
                return 'border border-primary text-primary py-[13px] px-4 rounded-4xl flex items-center justify-center gap-2';
            case 'link':
                return 'text-secondary-blue underline';
            default:
                return '';
        }
    }

    addMinors(
        event: [FormArray<FormGroup>, CustomerUIMetadataContainerDTOModel[]]
    ): void {
        this.addMinorServiceBL.addMinors(
            event[0],
            this.primaryAccountData(),
            event[1]
        );
    }
}
