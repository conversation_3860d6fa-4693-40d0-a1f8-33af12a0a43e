<button
    type="button"
    class="w-full font-medium md:max-w-[300px]"
    [ngClass]="customClasses"
    (click)="addMinorServiceBL.showAddParticipantModal.set(true)"
>
    @if ( variant() === 'outline') {
    <img src="assets/icons/person-plus.svg" alt="Add Minor" />
    }
    {{ label() }}
</button>

<ng-template #addMinorForm> <app-add-minor-form-modal (submit)="addMinors($event)" [loading]="addMinorServiceBL.loading()" /> </ng-template>
<lib-modal
    [isOpen]="addMinorServiceBL.showAddParticipantModal()"
    [modalContent]="addMinorForm"
    (closeModal)="addMinorServiceBL.showAddParticipantModal.set(false)"
    dialogueHeader="Add Participant"
>
</lib-modal>
