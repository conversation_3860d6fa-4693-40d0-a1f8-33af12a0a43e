<main class="max-w-[534px] mx-auto flex flex-col items-center mb-36 md:mb-0">
    <!-- Header Section -->
    <header
        class="md:px-6 md:pt-6 md:text-center md:bg-surface-white rounded-t-4xl"
    >
        <h1 i18n="welcome-to-waivers.title"
            class="text-2xl font-medium text-primary mb-3 md:mb-6"
        >
            Welcome to Semnox waiver
        </h1>
        <p i18n="welcome-to-waivers.description" class="text-sm text-primary">
            To enjoy our services, each guest needs to sign a waiver. Don’t
            worry—it’s quick and easy! Just follow these simple steps:
        </p>
    </header>

    <!-- Steps Section -->
    <section
        class="w-full bg-white shadow-lg rounded-4xl md:rounded-t-none overflow-y-auto mt-5 md:mt-0"
    >
        <ol class="md:mt-6">
            @for (step of steps; track $index) {
            <li
                class="p-5 md:p-6 shadow-[0_-4px_10px_rgba(0,0,0,0.05)] rounded-t-4xl"
            >
                <article class="relative">
                    <span i18n="welcome-to-waivers.step-number"
                        class="block text-xs font-medium text-neutral-dark py-0.5 rounded-xl border-2 border-white"
                    >
                        Step {{ $index + 1 }}
                    </span>
                    <div class="flex items-center gap-4 mt-1">
                        <img
                            [src]="step.icon"
                            [alt]="step.alt"
                            class="w-12 h-12"
                        />
                        <div class="flex-1">
                            <h2
                                class="mb-1 text-base font-semibold text-primary"
                            >
                                {{ step.title }}
                            </h2>
                            <p class="text-sm text-neutral-dark">
                                {{ step.description }}
                            </p>
                        </div>
                    </div>
                </article>
            </li>
            }
        </ol>

        <!-- Desktop CTA -->
        <footer class="py-6 hidden md:flex flex-col items-center">
            <ng-container *ngTemplateOutlet="ctaTemplate"></ng-container>
        </footer>
    </section>

    <!-- Mobile CTA -->
    <lib-page-footer>
        <ng-container *ngTemplateOutlet="ctaTemplate"></ng-container>
    </lib-page-footer>
</main>

<!-- Shared CTA Template -->
<ng-template #ctaTemplate>
    <div class="flex flex-col items-center w-full">
    <button
        type="button"
        routerLink="/auth/register"
        class="w-full md:max-w-[315px] py-4 px-6 mb-4 font-medium text-white bg-gray-800 rounded-4xl"
    >
        <span i18n="welcome-to-waivers.register-button z">Register</span>
    </button>
    <p class="flex items-center gap-1 text-sm text-center text-primary">
        <span i18n="welcome-to-waivers.already-have-account">Already have an account?</span>
        <button
            type="button"
            routerLink="/auth/login"
            class="font-medium text-blue-500 hover:underline"
        >
                <span i18n="welcome-to-waivers.login-button">Login</span>
            </button>
        </p>
    </div>
</ng-template>
