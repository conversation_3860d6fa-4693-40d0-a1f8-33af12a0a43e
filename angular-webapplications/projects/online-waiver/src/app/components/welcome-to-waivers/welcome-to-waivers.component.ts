/**
 * @fileoverview Welcome to waivers component
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-07-17
 */
import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { PageFooterComponent } from 'lib-ui-kit';
import { RouterLink } from '@angular/router';

@Component({
    selector: 'app-welcome-to-waivers',
    imports: [PageFooterComponent, CommonModule, RouterLink],
    templateUrl: './welcome-to-waivers.component.html',
    styleUrl: './welcome-to-waivers.component.scss',
})
export class WelcomeToWaiversComponent {
    steps = [
        {
            icon: '/assets/icons/user-check.svg',
            alt: 'login-icon',
            title: $localize`:welcome-to-waivers.step1.title@@welcome-to-waivers.step1.title:Login to your account`,
            description: $localize`:welcome-to-waivers.step1.description@@welcome-to-waivers.step1.description:Log in to your account or register to get started.`,
        },
        {
            icon: '/assets/icons/sign.svg',
            alt: 'sign-icon',
            title: $localize`:welcome-to-waivers.step2.title@@welcome-to-waivers.step2.title:Select the waiver and sign`,
            description: $localize`:welcome-to-waivers.step2.description@@welcome-to-waivers.step2.description:Sign for yourself and your dependents (family & friends) to receive waiver acknowledgment.`,
        },
        {
            icon: '/assets/icons/check-in.svg',
            alt: 'check-in-icon',
            title: $localize`:welcome-to-waivers.step3.title@@welcome-to-waivers.step3.title:Check-in online`,
            description: $localize`:welcome-to-waivers.step3.description@@welcome-to-waivers.step3.description:Enter the code/OTP received during waiver signing and proceed`,
        },
    ];

}
