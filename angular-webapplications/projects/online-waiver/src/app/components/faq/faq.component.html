<div libClickOutside (clickOutside)="showFaq.set(false)" class="flex items-center">
  <button (click)="toggleFaq()">
    <img src="assets/icons/question-circle.svg" alt="FAQ" class="w-6 h-6" />
  </button>

  @if (showFaq()) {
    <div
      class="absolute top-14 right-2 md:right-10 flex flex-col gap-4 bg-white p-5 rounded-4xl w-full max-w-[340px] border border-surface"
    >
      <h1 class="text-lg font-semibold">FAQ</h1>
      <div class="flex flex-col gap-2">
        @for (item of faqItems(); track $index) {
          <div class="flex flex-col gap-3">
            @if ($index > 0) {
              <hr class="border-t border-surface" />
            }
            <div
              class="flex flex-row gap-2 items-center w-full justify-between cursor-pointer"
              (click)="toggleFaqItem($index)"
            >
              <h2 class="text-sm font-medium">{{ item.question }}</h2>
              <img
                src="assets/icons/chevron-down.svg"
                alt="chevron-down"
                class="w-[18px] h-[18px] transition-transform"
                [class.rotate-180]="selectedFaq() === $index"
              />
            </div>
            @if (selectedFaq() === $index) {
              <p class="text-sm">{{ item.answer }}</p>
            }
          </div>
        }
      </div>
    </div>
  }
</div>
