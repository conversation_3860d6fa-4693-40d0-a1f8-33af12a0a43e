import { Component, signal } from '@angular/core';
import { ClickOutsideDirective } from 'lib-ui-kit';

interface FaqItem {
  question: string;
  answer: string;
}

@Component({
    selector: 'app-faq',
    imports: [ClickOutsideDirective],
    templateUrl: './faq.component.html',
    styleUrl: './faq.component.scss',
})
export class FaqComponent {
    readonly showFaq = signal(false);
    readonly selectedFaq = signal<number | null>(null);

    toggleFaq() {
        this.showFaq.set(!this.showFaq());
    }

    toggleFaqItem(index: number) {
        this.selectedFaq.set(this.selectedFaq() === index ? null : index);
    }

    faqItems = signal<FaqItem[]>([
        {
            question: 'How do I sign a waiver?',
            answer: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
        },

        {
            question: 'How do I add a dependent?',
            answer: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
        },

        {
            question: 'How do I check-in?',
            answer: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
        },
        {
            question: 'How can I contact support?',
            answer: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
        },
    ]);
}
