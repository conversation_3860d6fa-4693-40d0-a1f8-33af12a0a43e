/**
 * @fileoverview ParticipantSectionComponent is a component that displays a participant section
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-07-30
 */
import { Component, input, output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ParticipantItemComponent } from '../participant-item/participant-item.component';
import { IParticipant } from '../../interface/customer.interface';

@Component({
    selector: 'app-participant-section',
    standalone: true,
    imports: [CommonModule, ParticipantItemComponent],
    templateUrl: './participant-section.component.html',
    styles: [],
})
export class ParticipantSectionComponent {
    participants = input.required<IParticipant[]>();
    selectedParticipants = input.required<IParticipant[]>();
    participantChange = output<{
        participant: IParticipant;
        checked: boolean;
    }>();

    onParticipantCheckboxChange(event: {
        participant: IParticipant;
        checked: boolean;
    }): void {
        this.participantChange.emit(event);
    }
}
