<div class="max-w-[30rem] py-5 px-2 md:p-6 grid max-390:gap-4 gap-6">
    <div class="flex flex-col items-center gap-5">
        <img src="assets/icons/document.svg" alt="Document" />
        <div class="flex flex-col gap-1">
            <h1 i18n="signed-waiver-confirmation.title" class="text-lg font-semibold text-center">
                W<PERSON><PERSON> signed successfully
            </h1>
            <p i18n="signed-waiver-confirmation.description" class="text-neutral-dark text-center max-320:text-xs text-sm">
                Use the waiver code to avail the service. We have also sent the waiver
                code on your email and SMS.
            </p>
        </div>
    </div>

    <div class="flex items-center justify-center gap-2 w-full bg-feedback-success py-[31px] rounded-3xl">
        <div class="flex flex-col gap-1 items-center justify-center text-white">
            <p i18n="signed-waiver-confirmation.waiver-code-label" class="text-xs text-center">Waiver Code</p>
            <p i18n="signed-waiver-confirmation.waiver-code" class="text-[32px] font-medium text-center">
                {{ signInCode() }}
            </p>
        </div>
    </div>

    <div class="flex flex-col w-full items-center justify-center gap-3 p-2 md:py-2 md:px-0">
        <button (click)="onDownloadWaiverDocument()"
            class="w-full py-3 bg-primary text-white max-320:text-sm text-base font-medium rounded-4xl disabled:bg-surface flex items-center justify-center gap-2 text-center">
            <img src="assets/icons/download.svg" alt="download" />
            <span i18n="signed-waiver-confirmation.download-waiver-button">Download waiver document</span>
        </button>
        <button (click)="onSignForMore()"
            class="w-full py-3 bg-transparent max-320:text-sm text-base text-primary border border-primary font-medium rounded-4xl disabled:bg-surface">
            <span i18n="signed-waiver-confirmation.sign-for-more-button">Sign for more</span>
        </button>
        <button (click)="onViewSignedWaiver()"
            class="w-full py-3 bg-transparent max-320:text-sm text-base text-primary border border-primary font-medium rounded-4xl disabled:bg-surface">
            <span i18n="signed-waiver-confirmation.view-signed-waiver-button">View signed waiver</span>
        </button>
        <button (click)="onLogout()" class="pl-0 text-secondary-blue text-sm underline">
            <span i18n="signed-waiver-confirmation.logout-button">Logout</span>
        </button>
    </div>
</div>