<div class="px-4 pb-4 md:pb-6 md:px-6 lg:px-8 min-w-md">
    <!-- Loading State -->
    @if (isLoading()) {
    <div
        class="bg-surface-lighter rounded-4xl w-full overflow-hidden min-h-[75vh] md:min-h-[85vh] flex items-center justify-center">
        <div class="text-center">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p class="text-gray-600 font-medium">Loading waiver preview...</p>
        </div>
    </div>
    } @else if (hasError()) {
    <!-- Error State -->
    <div
        class="bg-surface-lighter rounded-4xl w-full overflow-hidden min-h-[70vh] md:min-h-[80vh] min-w-[50vw] flex items-center justify-center">
        <div class="text-center">
            <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <img src="assets/icons/error-warning.svg" alt="Warning" class="w-8 h-8" />
            </div>
            <h3 class="text-lg font-semibold text-gray-800 mb-2" i18n="preview-waiver.no-waiver-found-title">No Waiver
                Found</h3>
            <p class="text-gray-600 mb-4" i18n="preview-waiver.no-waiver-found-description">The waiver content could not
                be loaded.</p>
            <p class="text-sm text-gray-500" i18n="preview-waiver.contact-support-message">Please contact support for
                assistance.</p>
        </div>
    </div>
    } @else {
    <!-- Content Display -->
    <div class="rounded-4xl w-full overflow-hidden flex justify-center">
        <div class="w-full overflow-hidden">
            <iframe [src]="iframeSrc()"
                class="min-h-[70vh] md:min-h-[80vh] min-w-[50vw] max-w-full object-contain pt-2 w-full" frameborder="0"
                allowfullscreen title="Waiver Preview">
            </iframe>
        </div>
    </div>
    }
</div>