/**
 * @fileoverview
 * Provides the preview of selected waiver set details includes the preview of HTML and Pdf Form waivers
 *
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024-09-18
 */

import { Component, input, computed, inject } from '@angular/core';
import { WaiverPreviewBLService } from '../../services/business-layer/waiver-preview-bl.service';
import { WaiverPreviewData } from '../../interface/waiver.interface';

@Component({
    selector: 'app-preview-waiver',
    standalone: true,
    imports: [],
    templateUrl: './preview-waiver.component.html',
    styleUrl: './preview-waiver.component.scss',
    providers: [WaiverPreviewBLService],
})
export class PreviewWaiverComponent {
    waiverData = input<WaiverPreviewData | null>(null);
    private readonly _waiverPreviewService = inject(WaiverPreviewBLService);

    /**
     * Computed property that returns the safe URL for the iframe
     * Handles both HTML and PDF data with proper base64 decoding
     */
    readonly iframeSrc = computed(() => {
        const data = this.waiverData();
        return this._waiverPreviewService.processWaiverData(data);
    });

    /**
     * Computed property to determine if content is loading
     */
    readonly isLoading = computed(() => !this.waiverData());

    /**
     * Computed property to check if there's an error (empty base64 data)
     */
    readonly hasError = computed(() => {
        const data = this.waiverData();
        return data && (!data.base64Data || data.base64Data.trim() === '');
    });
}
