
    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
      @for (_ of skeletonItems; track $index) {
        <div role="status" class="bg-white p-6 rounded-4xl shadow-lg animate-pulse overflow-hidden">
          <div class="bg-gray-200 h-14 rounded-3xl mb-6"></div>
          <div class="grid grid-cols-2 gap-4 mb-4">
            <div><div class="bg-gray-200 h-4 w-24 rounded mb-1"></div><div class="bg-gray-200 h-5 w-16 rounded"></div></div>
            <div><div class="bg-gray-200 h-4 w-24 rounded mb-1"></div><div class="bg-gray-200 h-5 w-16 rounded"></div></div>
          </div>
          <div class="grid grid-cols-2 gap-4 mb-6">
            <div><div class="bg-gray-200 h-4 w-24 rounded mb-1"></div><div class="bg-gray-200 h-5 w-20 rounded"></div></div>
            <div><div class="bg-gray-200 h-4 w-24 rounded mb-1"></div><div class="bg-gray-200 h-5 w-20 rounded"></div></div>
          </div>
          <div class="border-b-2 border-gray-300 opacity-50 mb-6"></div>
          <div class="grid grid-cols-2 gap-4">
            <div class="bg-gray-200 h-6 rounded w-32"></div>
            <div class="bg-gray-200 h-6 rounded w-32"></div>
          </div>
        </div>
      }
    </div>
  
  