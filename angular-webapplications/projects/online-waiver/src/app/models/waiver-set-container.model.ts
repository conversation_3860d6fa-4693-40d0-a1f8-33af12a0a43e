/**
 * @fileoverview Model for waiver set container data structure
 * <AUTHOR>
 * @version 1.0.0
 */
export interface WaiverSetContainerResponseFromAPI {
    data: WaiverSetContainerResponse;
}

export interface WaiverSetContainerResponse {
    WaiverSetContainerDTOList: WaiverSetContainerDTOList[];
    Hash: string;
}

export interface WaiverSetContainerDTOList {
    WaiverSetId: number;
    Name: string;
    WaiversContainerDTO: WaiversContainerDTO[];
    WaiverSetSigningOptionsContainerDTO: WaiverSetSigningOptionsContainerDTO[];
}

export interface WaiverSetSigningOptionsContainerDTO {
    LookupValueId: number;
    OptionName: string;
    OptionDescription: string;
}

export interface WaiversContainerDTO {
    Name: string;
    WaiverFileName: string;
    ValidForDays: null;
    EffectiveDate: Date;
    EffectiveTillDate: Date | null;
    WaiverSetDetailId: number;
    WaiverSetId: number;
    WaiverHTML: string;
    WaiverPdfBase64: null | string;
}

export interface SignatoryCustomerDTO {
    CustomerId: number;
    CustomerName: string;
}

export interface SigningForCustomerDTO {
    CustomerId: number;
    CustomerName: string;
}

export interface GetHtmlWaiverPayload {
    WaiverSetId: number;
    WaiverSetDetailId: number;
    SignForCustomersIdList: number[] | null;
    Name: string;
    SignMessage: string;
    SignatoryCustomerDTO: SignatoryCustomerDTO;
    SigningForCustomerDTOList: SigningForCustomerDTO[];
    CustomerWaiverHTMLForm: string;
    SuccessButtonURL: string;
    CancelButtonURL: string;
    DocumentIdentifier: string;
    DynamicPlaceHolders: string[];
}

export interface GetHtmlWaiverModel {
    data: {
        WaiverSetId: number;
        Channel: string | null;
        CreateCustomerWaiverDTOList: GetHtmlWaiverPayload[];
    };
}
