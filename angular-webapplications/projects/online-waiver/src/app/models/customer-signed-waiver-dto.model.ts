/**
 * @fileoverview CustomerSignedWaiverDTO is a data transfer object for customer signed waiver information
 * <AUTHOR> @version 1.0.0
 * @created 2025-08-12
 */
/**
 * @class CustomerSignedWaiverDTO
 * @description
 * Data transfer object for customer signed waiver information. This class extends BaseDTO
 * and contains all the properties related to a customer's signed waiver including
 * identification, metadata, content, and status information.
 *
 * @usage
 * This DTO is used throughout the application to represent signed waiver data
 * in API requests and responses, as well as in component data binding
 *
 * @properties
 * - CustomerSignedWaiverId: Unique identifier for the signed waiver
 * - CustomerSignedWaiverHeaderId: Header ID for the signed waiver
 * - WaiverSetDetailId: ID of the waiver set detail
 * - SignedWaiverFileName: Name of the signed waiver file
 * - WaiverName: Name of the waiver
 * - WaiverFileName: Name of the original waiver file
 * - SignedFor: ID of the person the waiver was signed for
 * - SignedForName: Name of the person the waiver was signed for
 * - ExpiryDate: Expiration date of the waiver
 * - IsActive: Whether the waiver is currently active
 * - SignedWaiverFileContentInBase64Format: Base64 encoded content of the signed waiver
 * - And many more properties for comprehensive waiver management
 *
 * @dependencies
 * - BaseDTO: Base class for data transfer objects
 *
 * @interfaces
 * - CustomerSignedWaiverResponse: Response type for customer signed waiver API
 */

import { BaseDTO } from "lib-app-core";

/**
 * Data transfer object for customer signed waiver information
 */
export class CustomerSignedWaiverDTO extends BaseDTO<CustomerSignedWaiverDTO>{
    CustomerSignedWaiverId!: number | null;
    CustomerSignedWaiverHeaderId!: number | null;
    WaiverSetDetailId!: number | null;
    SignedWaiverFileName!: string | null;
    WaiverName!: string | null;
    WaiverFileName!: string | null;
    SignedFor!: number | null;
    SignedForName!: string | null;
    ExpiryDate!: string | null;
    IsActive!: boolean | null;
    DeactivatedBy!: string | null;
    DeactivationDate!: string | null;
    DeactivationApprovedBy!: string | null;
    Guid!: string | null;
    SynchStatus!: boolean | null;
    MasterEntityId!: number | null;
    CreatedBy!: string | null;
    CreationDate!: string | null;
    LastUpdatedBy!: string | null;
    LastUpdateDate!: string | null;
    SiteId!: number | null;
    WaiverSignedImageList!: any[] | null;
    CustomerContentForWaiverDTOList!: any[] | null;
    SignedBy!: number | null;
    SignedByName!: string | null;
    SignedDate!: string | null;
    WaiverCode!: string | null;
    WaiverSetId!: number | null;
    WaiverSetDescription!: string | null;
    SignedWaiverFileContentInBase64Format!: string | null;
    GuardianId!: number | null;
    IsChangedRecursive!: boolean | null;
    IsChanged!: boolean | null;

    constructor() {
        super();
    }
}

// Response type for customer signed waiver API
export interface CustomerSignedWaiverResponse {
    data: CustomerSignedWaiverDTO[];
}