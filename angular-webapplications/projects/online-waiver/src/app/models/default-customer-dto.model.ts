/**
 * @fileoverview Model for default customer DTO data structure
 * <AUTHOR>
 * @version 1.0.0
 */

import { UserAddressDTOModel } from '../../../../../lib/lib-auth/src/lib/models/user-address-dto.model';
import { BaseDTO } from 'lib/lib-app-core/src/lib/models/base-dto.model';
import { UserLoginDTOModel } from '../../../../../lib/lib-auth/src/lib/models/user-login-dto.model';

// Extended address DTO for latest address with additional properties
export interface DefaultCustomerLatestAddressDTO extends UserAddressDTOModel {
    IsDefault: boolean;
    ContactDTOList: any[];
    IsChanged: boolean;
    IsChangedRecursive: boolean;
}

// Extend UserLoginDTOModel to reuse all existing properties and add default customer specific ones
export class DefaultCustomerDTOModel extends UserLoginDTOModel {
    // Additional properties specific to default customer
    SecondaryPhoneNumber!: string;
    FBUserId!: string;
    FBAccessToken!: string;
    TWAccessToken!: string;
    TWAccessSecret!: string;
    WeChatAccessToken!: string;
    IsChangedRecursive!: boolean;
    CustomerCuponsDT: any | null = null;
    AccountDTOList: any[] = [];
    CustomerMembershipProgressionDTOList: any[] = [];
    CustomerMembershipRewardsLogDTOList: any[] = [];
    CustomerSignedWaiverDTOList: any[] = [];
    ActiveCampaignCustomerInfoDTOList: any[] = [];
    LastVisitedDate!: string;

    // Override LatestAddressDTO to use the extended version
    declare LatestAddressDTO: DefaultCustomerLatestAddressDTO;
}

export interface DefaultCustomerResponse
    extends BaseDTO<DefaultCustomerDTOModel> {
    data: DefaultCustomerDTOModel[];
}
