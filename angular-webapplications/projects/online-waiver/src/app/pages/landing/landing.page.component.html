<!-- <button (click)="update(1010)" type="button"
        class="bg-accent-700 hover:bg-accent-800 focus:ring-4 focus:ring-accent-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 focus:outline-none">Update
        1010</button> -->
<!-- <pre> -->
<!-- <div *ngIf="languageContainerService.data$ | async as data"> -->

<!-- <div class="break-words text-sm">
    {{ localVar | json }}
</div> -->

<!-- </pre> -->
<!-- </div> -->

<!-- {{languageContainerService.data?.date}} -->

<!-- existing code starts  -->
<!-- <p *ngIf="apiData; else loading">
        <strong>Login Response:</strong> {{ apiData.loginResponse | json }} <br>
    </p>
    <ng-template #loading>
        <p>Loading data...</p>
    </ng-template>
    <br /><br />
    
    <button (click)="update(1012)" type="button"
        class="text-white bg-accent-700 hover:bg-accent-800 focus:ring-4 focus:ring-accent-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 focus:outline-none">Update
        1012</button>
    <br />
    <button (click)="update(1014)" type="button"
        class="text-white bg-accent-700 hover:bg-accent-800 focus:ring-4 focus:ring-accent-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 focus:outline-none">Update
        1013</button>
    <br />
    <button (click)="update(1010)" type="button"
        class="text-white bg-accent-700 hover:bg-accent-800 focus:ring-4 focus:ring-accent-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 focus:outline-none">Update
        1010</button>
    <br />
    
    <div class="relative w-1/3" *ngIf="waiverSetContainerResponse != null">
        <label for="waiver-set" class="block text-sm font-medium text-gray-700">Select waiver</label>
        <select id="waiver-set" name="waiver-set" (change)="onSelectedWaiverSet($event)"
            class="appearance-none mt-1 block w-full px-4 py-2 text-base text-gray-700 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 pr-10">
            <option *ngFor="let waiverSet of waiverSetContainerResponse?.WaiverSetContainerDTOList"
                [value]="waiverSet.WaiverSetId">{{waiverSet.Name}}
            </option>
        </select>
        <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
            <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-gray-500" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
            </svg>
        </div>
    </div>
        
    <section class="pt-7 pb-3 flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div class="mb-2 sm:mb-0">
            <h1 class="text-3xl font-extrabold mb-2">Sign waivers</h1>
            <p>Sign waivers to get access to the services</p>
        </div>
        <div class="flex sm:block justify-between">
            <a href="" class="text-primary-500 underline text-sm font-semibold mr-2">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                    stroke="currentColor" class="size-5 inline">
                    <path stroke-linecap="round" stroke-linejoin="round"
                        d="M9.879 7.519c1.171-1.025 3.071-1.025 4.242 0 1.172 1.025 1.172 2.687 0 3.712-.203.179-.43.326-.67.442-.745.361-1.45.999-1.45 1.827v.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 5.25h.008v.008H12v-.008Z" />
                </svg>
                How to sign waivers?
            </a>
            <a class="text-primary-500 underline text-sm font-semibold" (click)="openPrevieWaiverComponent()">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                    stroke="currentColor" class="size-5 inline">
                    <path stroke-linecap="round" stroke-linejoin="round"
                        d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z" />
                </svg>
                Preview waiver
            </a>
        </div>
    </section>
    
    <section class="py-6">
    
        <ul class="flex flex-wrap text-sm font-medium text-gray-800 text-center sm:text-left">
            <li
                class="flex-auto sm:flex-none inline-block rounded-t-2xl hover:bg-gray-50 border-x-2 border-t-2 border-primary-500 py-6 px-5 sm:py-8 sm:px-10">
                <a href="#" aria-current="page" class=" active">
                    <span>New Customer?</span>
                    <span class="font-bold text-xl text-primary-500 block">Register</span>
                </a>
            </li>
            <li
                class="flex-auto sm:flex-none inline-block rounded-t-2xl hover:bg-gray-50 border-r-2 border-t-2 border-gray-400 py-6 px-5 sm:py-8 sm:px-10">
                <a href="#" class="">
                    <span>Existing Customer?</span>
                    <span class="font-bold text-xl text-primary-500 block">Login</span>
                </a>
            </li>
        </ul>
        <div class="border-2 border-primary-500 rounded-b-2xl">
    
            <form class="w-full py-6 px-8">
                <app-register-form></app-register-form>
    
                <div class="mt-6">
                    <button type="button" class="{{getBtnPrimary()}} mb-4 sm:mb-0">Primary Landing</button>
                    <button type="button" class="{{getBtnAccent()}} mb-4 sm:mb-0">Accent Landing</button>
                </div>
    
                <div class="mt-12 flex flex-col sm:flex-row items-center">
                    <button type="button" class="{{getBtnPrimary({id:'register_btn', size: 'xl'})}} mb-4 sm:mb-0"
                        id="register_btn">
                        Register and Sign Waiver
                    </button>
    
                    <p class="text-gray-800 text-sm text-center ml-4">
                        Already have an account?
                        <a href="javascript:void(0);" class="text-blue-600 font-semibold hover:underline ml-1">Login
                            here</a>
                    </p>
                </div>
            </form>
    
        </div>
    
    </section>
    
     <pre>{{selectedWaiverSet  |json}}</pre>
     <pre>{{isModalOpen}}</pre>
    <div *ngIf="selectedWaiverSet != null && isModalOpen">
        <app-preview-waiver [isOpen]="isModalOpen" (close)="closeModal()"
            [selectedWaiverSetDetailsForPreview]="selectedWaiverSet"></app-preview-waiver>
    </div> -->
<!-- existing code ends -->

<app-welcome-to-waivers />
