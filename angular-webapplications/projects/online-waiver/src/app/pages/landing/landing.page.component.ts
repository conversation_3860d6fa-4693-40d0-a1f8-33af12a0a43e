/**
 * @fileoverview This is the main landing component (home page) for the application.
 *
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024-09-18
 */

import { CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { LandingPageTheme } from '@theme/waiver/waiver.theme';
import {
    PageInterface,
    PARENT_FUNCTION_TOKEN,
    SiteViewsDTOModel,
    SiteViewsServiceDL,
} from 'lib-app-core';
import { WelcomeToWaiversComponent } from '../../components/welcome-to-waivers/welcome-to-waivers.component';
import { PreviewWaiverService } from '../../core/preview-waiver.service';
import { SiteBaseComponent } from '../../core/sitebase.component';
import {
    WaiverSetContainerDTOList,
    WaiverSetContainerResponse,
    WaiverSetContainerResponseFromAPI,
} from '../../models/waiver-set-container.model';

@Component({
    standalone: true,
    selector: 'app-landing',
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        RouterModule,
        WelcomeToWaiversComponent,
    ],
    templateUrl: './landing.page.component.html',
    styleUrl: './landing.page.component.scss',
    providers: [
        {
            provide: PARENT_FUNCTION_TOKEN,
            useFactory: (parent: LandingPageComponent) => {
                return parent.getPageTheme
                    ? () => parent.getPageTheme()
                    : () => {};
            },
            deps: [LandingPageComponent], // Ensure that the parent component is resolved
        },
    ],
})
export class LandingPageComponent
    extends SiteBaseComponent
    implements PageInterface
{
    //appInitService = inject(AppInitService);
    // private _languageContainerServiceDL = inject(LanguageContainerServiceDL);
    // private _parafaitDefaultContainerService = inject(ParafaitDefaultContainerService);
    private _siteViewsService = inject(SiteViewsServiceDL);

    /* preview waiver */
    waiverSetContainerResponse: WaiverSetContainerResponse | any = null;
    selectedWaiverSet: WaiverSetContainerDTOList | any = null;
    isModalOpen = false; //to close and open modal, this will be replaced by new html

    localVar: any;

    constructor(
        private router: Router,
        private previewWaiverService: PreviewWaiverService
    ) {
        super();

        // this._languageContainerServiceDL.subscribeToData(data => {
        //     this.localVar = data;
        // });

        // this._parafaitDefaultContainerService.subscribeToData((dtoList: ParafaitDefaultContainerDTOModel[]) => {
        //     // this.localVar = data;
        //     console.log(dtoList);
        //     this.localVar =
        //         ParafaitDefaultContainerDTOModel.findByName(
        //             dtoList,
        //             'GOOGLE_RECAPTCHA_CLIENT_ID'
        //         );
        // });

        this._siteViewsService.subscribeToData(
            (dtoList: SiteViewsDTOModel[]) => {
                // this.localVar = data;
                console.log(dtoList);
                this.localVar = SiteViewsDTOModel.findBySiteId(dtoList, 1010);
            }
        );
    }

    protected getRelevantTheme() {
        return LandingPageTheme;
    }

    getPageTheme() {
        return this.getRelevantTheme();
    }

    update(siteId: number) {
        // this.appInitService.updateSiteId(siteId).then(() => {
        //     // this.fetchWaiverSetContainer();
        // })
    }

    /* Preview related code */
    //fetch waiver set container response from API
    selectedWaiver: any = null;
    fetchWaiverSetContainer() {
        this.previewWaiverService.getWaiverSetContainer().subscribe({
            next: (
                waiverSetContainerResp: WaiverSetContainerResponseFromAPI | any
            ) => {
                if (waiverSetContainerResp && waiverSetContainerResp?.data) {
                    this.waiverSetContainerResponse =
                        waiverSetContainerResp?.data;
                }
            },
            error: (error) => {
                console.log(
                    'fetchAndProcessWaiverSetContainer()' +
                        JSON.stringify(error)
                );
            },
        });
    }

    onSelectParticipantAndSignWaiver() {
        let selectedWaiverSetId = 1; //this as to be replaced with selectedWaiverSetId
        let loggedInCustId = 34;
        this.router.navigate([
            '/sign-waiver',
            selectedWaiverSetId,
            loggedInCustId,
        ]);
    }

    openPrevieWaiverComponent() {
        this.isModalOpen = true;
    }

    // Method to open the modal
    openModal() {
        this.isModalOpen = true;
    }

    // Method to close the modal
    closeModal() {
        this.isModalOpen = false;
    }

    onSelectedWaiverSet(event: Event) {
        const selectedOption: any = (event.target as HTMLSelectElement).value;
        this.selectedWaiverSet =
            this.waiverSetContainerResponse.WaiverSetContainerDTOList.find(
                (ws: WaiverSetContainerDTOList) =>
                    ws.WaiverSetId == selectedOption
            );
    }
}
