/**
 * @fileoverview MySignedWaiversComponent is a page component that displays user's signed waivers
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-08-12
 */
/**
 * @component MySignedWaiversComponent
 * @description
 * Page component for displaying user's signed waivers. This component manages the
 * display of all signed waivers for the current user, handles loading states,
 * and provides access to signed waiver functionality.
 *
 * @usage
 * This component is used as a page within the my-account section to display
 * all signed waivers for the authenticated user
 *
 * @inputs
 * - No inputs defined in this component
 *
 * @outputs
 * - No outputs defined in this component
 *
 * @dependencies
 * - SignedWaiverBL: Business logic service for signed waivers
 * - SignedWaiverDL: Data layer service for signed waivers
 * - SignedWaiverComponent: Component for displaying individual signed waivers
 * - WaiverCardSkeletonComponent: Component for loading skeleton
 *
 * @methods
 * - ngOnInit(): Lifecycle hook that loads signed waivers on component initialization
 */

import { CommonModule } from '@angular/common';
import {
    Component,
    inject,
    OnInit,
} from '@angular/core';
import { SignedWaiverComponent } from '../../../components/signed-waiver/signed-waiver.component';
import { WaiverCardSkeletonComponent } from '../../../components/waiver-card-skeleton/waiver-card-skeleton.component';
import { SignedWaiverBL } from '../../../services/business-layer/signed-waiver-bl.service';
import { SignedWaiverDL } from '../../../services/data-layer/signed-waiver-dl.service';

/**
 * Page component for displaying user's signed waivers
 */
@Component({
    selector: 'app-my-signed-waivers',
    imports: [CommonModule, SignedWaiverComponent, WaiverCardSkeletonComponent],
    providers:[SignedWaiverDL,SignedWaiverBL],
    templateUrl: './my-signed-waivers.component.html',
    styleUrl: './my-signed-waivers.component.scss',
})
export class MySignedWaiversComponent implements OnInit {
    private signedWaiverBL = inject(SignedWaiverBL);

    signedWaivers = this.signedWaiverBL.signedWaivers;
    isLoading = this.signedWaiverBL.loading;
    errorMessage = this.signedWaiverBL.errorMessage;
  
    ngOnInit(): void {
        this.signedWaiverBL.loadSignedWaivers();
    }

}
