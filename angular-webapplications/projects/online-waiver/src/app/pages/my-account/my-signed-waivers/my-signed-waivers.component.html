<div
    class="flex flex-col gap-5 lg:gap-6 bg-surface-lighter md:bg-surface-white rounded-4xl md:shadow-lg md:p-6 md:min-h-[calc(100vh-168px)]"
>
    <h1 i18n="my-signed-waivers.title" class="text-lg font-semibold text-primary">My Signed Waivers</h1>
    @if(errorMessage()) {
    <div class="flex justify-center items-center">
        <p i18n="my-signed-waivers.error-message" class="error-message">{{ errorMessage() }}</p>
    </div>
    } 
    @else if(isLoading()){
        <app-waiver-card-skeleton></app-waiver-card-skeleton>
    }
    @else if(signedWaivers().length === 0)
    {
    <div class="flex justify-center items-center">
        <p i18n="my-signed-waivers.no-signed-waivers-found">No Signed Waivers found</p>
    </div>
    }
    <!-- Waivers Grid - Responsive layout -->
    @else{
    <app-signed-waiver [signedWaivers]="signedWaivers()"></app-signed-waiver>
    }
</div>
