@let primaryParticipant = primaryParticipant$ | async;
@let minorParticipants = minorParticipants$ | async;
<div class="min-h-[calc(100vh-168px)] bg-transparent md:bg-surface-white rounded-4xl mb-20 md:mb-0">
    <!-- Main Content Container -->
    <div class="grid gap-5 md:gap-6 p-0 md:p-6">
        <h1 i18n="my-relations.title" class="text-lg font-semibold text-primary">My Relations</h1>
        
        <!-- Primary Account Holder -->
        @if (primaryParticipant) {
            @if (primaryParticipant.loading) {
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="bg-surface-white rounded-3xl shadow-lg p-6 w-full">
                        <div class="animate-pulse">
                            <div class="h-3 bg-gray-200 rounded w-1/3 mb-2"></div>
                            <div class="h-4 bg-gray-200 rounded w-2/3"></div>
                        </div>
                    </div>
                </div>
            } @else if (primaryParticipant.error) {
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="bg-red-50 rounded-3xl shadow-lg p-6 w-full">
                        <p class="text-red-600 text-sm">{{ primaryParticipant.error }}</p>
                    </div>
                </div>
            } @else if (primaryParticipant.data) {
                <app-relations-item [participants]="[primaryParticipant.data]" />
            }
        }

        <!-- Minor Relations -->
        @if (minorParticipants) {
            @if (minorParticipants.loading) {
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="bg-surface-white rounded-3xl shadow-lg p-6 w-full">
                        <div class="animate-pulse">
                            <div class="h-3 bg-gray-200 rounded w-1/4 mb-2"></div>
                            <div class="h-4 bg-gray-200 rounded w-3/4"></div>
                        </div>
                    </div>
                    <div class="bg-surface-white rounded-3xl shadow-lg p-6 w-full">
                        <div class="animate-pulse">
                            <div class="h-3 bg-gray-200 rounded w-1/4 mb-2"></div>
                            <div class="h-4 bg-gray-200 rounded w-3/4"></div>
                        </div>
                    </div>
                </div>
            } @else if (minorParticipants.error) {
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="bg-red-50 rounded-3xl shadow-lg p-6 w-full">
                        <p class="text-red-600 text-sm">{{ minorParticipants.error }}</p>
                    </div>
                </div>
            } @else if (minorParticipants.data && minorParticipants.data.length > 0) {
                <app-relations-item [participants]="minorParticipants.data" />
            }
        }

        <!-- Divider -->
        <div class="w-full border-b-2 border-surface opacity-30 hidden md:block"></div>

        <!-- Add Minor Button -->
        <div class="hidden md:block">
            <ng-container *ngTemplateOutlet="addMinorButton"></ng-container>
        </div>
    </div>
</div>

<!-- Footer CTA (Mobile) -->
<lib-page-footer>
    <ng-container *ngTemplateOutlet="addMinorButton"></ng-container>
</lib-page-footer>


<ng-template #addMinorButton> 
    @if (primaryParticipant && primaryParticipant.data) {
                <app-add-minor-btn [label]="'Add Participant'" [variant]="'outline'"
                [primaryAccountData]="primaryParticipant.data" (addMinorSuccess)="onAddMinorSuccess($event)" />
                }
</ng-template>