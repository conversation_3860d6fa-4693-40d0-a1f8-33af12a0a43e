/**
 * @fileoverview Data layer service for handling waiver set API calls
 * <AUTHOR>
 * @version 1.0.0
 */
import { Injectable } from '@angular/core';
import { ApiServiceBase, areEqual } from 'lib-app-core';
import { Observable, map } from 'rxjs';
import { GetAllWaiverSetParams } from '../../interface/reservation.interface';
import { WaiverSetResponse } from '../../interface/waiver.interface';

@Injectable({ providedIn: 'root' })
export class WaiverSetDL extends ApiServiceBase {
    private _apiParams!: GetAllWaiverSetParams;

    constructor() {
        super('get_all_waiver_set', 'GET_ALL_WAIVER_SET');
        this.init();
    }

    buildApiParams(siteId: string, languageId: string) {
        this._apiParams = {
            siteId,
            languageId,
        };
    }

    //Builds the API parameters for the transaction details request
    //This method is called by the business layer to set the parameters before making the API call
    load(): Observable<WaiverSetResponse> {
        const { siteId, languageId } = this._apiParams;
        const url = this.getApiUrl()
            .replace('{siteId}', siteId)
            .replace('{languageId}', languageId);
        return this._http.get<WaiverSetResponse>(url);
    }
}
