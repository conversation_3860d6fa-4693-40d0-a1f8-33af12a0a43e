/**
 * @fileoverview Data layer service for handling get html waiver API calls
 * <AUTHOR>
 * @version 1.0.0
 */
import { Injectable } from '@angular/core';
import { ApiServiceBase } from 'lib-app-core';
import { Observable } from 'rxjs';
import {
    GetHtmlWaiverQueryParams,
    GetHtmlWaiverPayload,
} from '../../interface/waiver.interface';
import { GetHtmlWaiverModel } from '../../models/waiver-set-container.model';

@Injectable({ providedIn: 'root' })
export class GetHtmlWaiverDL extends ApiServiceBase {
    private _queryParams!: GetHtmlWaiverQueryParams;
    private _payload!: GetHtmlWaiverPayload;

    constructor() {
        super('get_html_waiver', 'GET_HTML_WAIVER');
        this.init();
    }

    /**
     * Builds the query parameters for the API request
     * @param data - Query parameters for the request
     */
    buildQueryParams(data: GetHtmlWaiverQueryParams) {
        this._queryParams = data;
    }

    /**
     * Builds the payload for the API request
     * @param data - Payload data for the request
     */
    buildPayload(data: GetHtmlWaiverPayload) {
        this._payload = data;
    }

    /**
     * Loads data by making API call with both query parameters and payload
     * @returns Observable with the API response
     */
    load(): Observable<GetHtmlWaiverModel> {
        // Build URL with query parameters
        const payload = this._payload;
        let url = this.getApiUrl();
        const { defaultCustomer, waiverSetId } = this._queryParams;
        url = url
            .replace('{DefaultCustomer}', defaultCustomer)
            .replace('{WaiverSetId}', waiverSetId);

        // Make POST request with payload
        return this._http.post<GetHtmlWaiverModel>(url, payload);
    }
}
