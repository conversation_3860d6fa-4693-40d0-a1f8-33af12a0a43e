/**
 * @fileoverview Data layer service for handling transaction details API calls
 * <AUTHOR>
 * @version 1.0.0
 */
import { Injectable } from '@angular/core';
import { ApiServiceBase } from 'lib-app-core';
import { DefaultCustomerResponse } from 'projects/online-waiver/src/app/models/default-customer-dto.model';
import { Observable } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class CreateDefaultCustomerDL extends ApiServiceBase {

    constructor() {
        super('create_default_customer', 'CREATE_DEFAULT_CUSTOMER');
        this.init();
    }
    /**
     * Loads the default customer data from the API
     * @returns Observable<any>
     */
    load(): Observable<DefaultCustomerResponse> {
        return this._http.get<DefaultCustomerResponse>(this.getApiUrl())
    }
}