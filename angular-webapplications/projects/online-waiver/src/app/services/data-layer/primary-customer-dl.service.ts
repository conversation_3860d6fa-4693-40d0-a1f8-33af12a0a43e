/**
 * @fileoverview PrimaryCustomerDL is a service that provides data layer for the primary customer
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-07-30
 */
import { Injectable } from '@angular/core';
import { ApiServiceBase } from 'lib-app-core';
import { UserLoginDTOModel } from 'lib/lib-auth/src/lib/models/user-login-dto.model';
import { Observable } from 'rxjs';

interface PrimaryCustomerAPIParams {
    customerId: string;
}

interface PrimaryCustomerResponse {
    data: UserLoginDTOModel[];
    customersImage: string;
    customersIdImage: string;
    totalPages: number;
}

@Injectable({
    providedIn: 'root',
})
export class PrimaryCustomerDL extends ApiServiceBase {
    private _apiParams!: PrimaryCustomerAPIParams;

    constructor() {
        super('primary_customer_data', 'PRIMARY_RELATION');
        this.init();
    }

    buildApiParams(data: PrimaryCustomerAPIParams) {
        this._apiParams = data;
    }

    /**
     * Loads the primary customer data
     *
     * This method is used to load the primary customer data from the database
     *
     * @returns Observable<PrimaryCustomerResponse> - The primary customer data
     */
    load(): Observable<PrimaryCustomerResponse> {
        const { customerId } = this._apiParams;
        const url = this.getApiUrl().replace('{CustomerId}', customerId);

        return this._http.get<PrimaryCustomerResponse>(url);
    }
}
