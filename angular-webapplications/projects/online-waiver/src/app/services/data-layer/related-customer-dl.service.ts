/**
 * @fileoverview RelatedCustomerDL is a service that provides data layer for the related customer
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-07-30
 */
import { Injectable } from '@angular/core';
import { ApiServiceBase } from 'lib-app-core';
import { Observable } from 'rxjs';
import {
    CustomerAPIParams,
    RelatedCustomerResponse,
} from '../../interface/customer.interface';

@Injectable({
    providedIn: 'root',
})
export class RelatedCustomerDL extends ApiServiceBase {
    private _apiParams!: CustomerAPIParams;

    constructor() {
        super('customer_relationship_data', 'CUSTOMER_RELATION');
        this.init();
    }

    buildApiParams(data: CustomerAPIParams) {
        this._apiParams = data;
    }

    /**
     * Loads the related customer data
     *
     * This method is used to load the related customer data from the database
     *
     * @returns Observable<RelatedCustomerResponse> - The related customer data
     */
    load(): Observable<RelatedCustomerResponse> {
        const { customerId } = this._apiParams;
        const url = this.getApiUrl().replace('{CustomerId}', customerId);

        return this._http.get<RelatedCustomerResponse>(url);
    }
}
