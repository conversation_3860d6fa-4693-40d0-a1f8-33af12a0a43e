/**
 * @fileoverview SignedWaiverDL is a data layer service for signed waiver API operations
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-08-12
 */
/**
 * @service SignedWaiverDL
 * @description
 * Data layer service for signed waiver API operations. This service extends ApiServiceBase
 * and handles all HTTP communication related to signed waiver data, including
 * building API parameters and making requests to the backend.
 *
 * @usage
 * This service is used by the business logic layer to communicate with the signed
 * waiver API endpoints and retrieve customer signed waiver data
 *
 * @inputs
 * - No inputs defined in this service
 *
 * @outputs
 * - load(): Observable that returns CustomerSignedWaiverResponse
 *
 * @dependencies
 * - ApiServiceBase: Base class for API service operations
 * - Observable: RxJS observable for handling async operations
 * - CustomerSignedWaiverResponse: Response type for customer signed waiver API
 *
 * @methods
 * - buildApiParams(): Sets customer ID for API request
 * - load(): Fetches signed waivers from API
 */

import {Injectable} from '@angular/core';
import { ApiServiceBase } from 'lib-app-core';
import { Observable } from 'rxjs';
import { CustomerSignedWaiverResponse } from '../../models/customer-signed-waiver-dto.model';

/**
 * Data layer service for signed waiver API operations
 */
@Injectable()
export class SignedWaiverDL extends ApiServiceBase {
    private _customerId: string|null = '';

    constructor() {
        super('signed_waivers_data', 'GET_SIGNED_WAIVERS');
        this.init();
    }

    /**
     * Sets customer ID for API request
     */
    buildApiParams(customerId: string|null) {
        this._customerId = customerId;
    }

    /**
     * Fetches signed waivers from API
     */
    load(): Observable<CustomerSignedWaiverResponse> {
        const url = `${this.getApiUrl()}?customerId=${this._customerId}`;
        return this._http.get<CustomerSignedWaiverResponse>(url);
    }
}
