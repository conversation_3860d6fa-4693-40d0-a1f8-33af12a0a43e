/**
 * @fileoverview Waiver Card State Management Service
 * @description This service manages the centralized state for all waiver-related data
 *              in the application. It provides reactive state management using RxJS
 *              observables and handles the complete lifecycle of waiver data including
 *              loading, filtering, error handling, and state transformations.
 * <AUTHOR> G
 * @version 1.0.0
 * @created 2025-01-27
 */

// Angular core imports for dependency injection and reactive programming
import { Injectable, inject, signal, computed } from '@angular/core';
import { map, switchMap, catchError, of, startWith } from 'rxjs';
import {
    WaiverSetContainerDTO,
    WaiverSetResponse,
} from '../../interface/waiver.interface';
import { WaiverDetailsServiceBL } from './waiver-details-bl.service';
import { PartyCardState } from '../../interface/reservation.interface';

/**
 * WaiverCardStateService - Centralized State Management for Waiver Operations
 *
 * This service acts as the single source of truth for all waiver-related state
 * in the application. It manages:
 * - Loading states for waiver data
 * - Error handling and recovery
 * - Waiver set filtering and processing
 * - Party type detection (single vs multiple parties)
 * - Reactive state updates using RxJS observables
 *
 * The service uses a reactive programming approach to ensure that all components
 * consuming waiver data stay synchronized and receive updates automatically.
 */
@Injectable()
export class WaiverCardStateService {
    // Dependency injection for waiver details business logic service
    private readonly _waiverDetailsBL = inject(WaiverDetailsServiceBL);

    /**
     * Main State Observable - Centralized waiver state management
     *
     * This observable manages the complete lifecycle of waiver data:
     * 1. Listens to waiver info changes from the business logic service
     * 2. Fetches waiver sets from the API
     * 3. Processes and filters waiver sets by effective dates
     * 4. Determines party type (single vs multiple)
     * 5. Handles loading states and error conditions
     * 6. Provides a consistent state structure for all consumers
     *
     * The observable uses RxJS operators for:
     * - switchMap: Sequential API calls based on waiver info changes
     * - map: Data transformation and filtering
     * - catchError: Error handling and recovery
     * - startWith: Initial loading state
     */
    readonly waiverState$ = this._waiverDetailsBL.waiverInfo$.pipe(
        switchMap((waiverInfo) => {
            // Clear any previous error messages when starting new operation
            this._waiverDetailsBL.errorMessage.set('');

            return this._waiverDetailsBL.getWaiverSet().pipe(
                // Step 1: Extract waiver sets from API response
                map((response: WaiverSetResponse) =>
                    this._waiverDetailsBL.getAllWaiverSetsByWaiverSetId(
                        response
                    )
                ),
                // Step 2: Filter waiver sets by effective dates (active waivers only)
                map((waiverSets: WaiverSetContainerDTO[]) =>
                    this._waiverDetailsBL.filterWaiverSetsByEffectiveDates(
                        waiverSets
                    )
                ),
                // Step 3: Transform into final state structure with party type detection
                map(
                    (waiverSets: WaiverSetContainerDTO[]): PartyCardState => ({
                        loading: false,
                        error: null,
                        waiverSets,
                        isSingleParty: this.isSingleParty(waiverSets),
                    })
                ),
                // Step 4: Handle any errors that occur during processing
                catchError(this.handleError.bind(this)),
                // Step 5: Start with loading state for better UX
                startWith(this.getLoadingState())
            );
        })
    );

    /**
     * Computed Observables - Derived state for specific use cases
     *
     * These observables provide easy access to specific parts of the state
     * without needing to manually extract them from the main state observable.
     * This promotes better separation of concerns and cleaner component code.
     */

    /**
     * Waiver Sets Observable - Provides direct access to waiver sets data
     *
     * @returns Observable<WaiverSetContainerDTO[]> - Array of filtered waiver sets
     */
    readonly waiverSets$ = this.waiverState$.pipe(
        map((state) => state.waiverSets)
    );

    /**
     * Loading State Observable - Provides loading status for UI feedback
     *
     * @returns Observable<boolean> - True when data is being loaded
     */
    readonly isLoading$ = this.waiverState$.pipe(map((state) => state.loading));

    /**
     * Error State Observable - Provides error information for error handling
     *
     * @returns Observable<string | null> - Error message or null if no error
     */
    readonly error$ = this.waiverState$.pipe(map((state) => state.error));

    /**
     * Single Party Detection Observable - Determines if this is a single party booking
     *
     * @returns Observable<boolean> - True if single party, false if multiple parties
     */
    readonly isSingleParty$ = this.waiverState$.pipe(
        map((state) => state.isSingleParty)
    );

    /**
     * Determines if the current booking is for a single party
     *
     * This method checks if there's only one waiver set available and if
     * waiver info exists, indicating a single party booking scenario.
     * Single party bookings may have different UI flows and business logic.
     *
     * @param waiverSets - Array of available waiver sets
     * @returns boolean - True if single party booking, false otherwise
     */
    private isSingleParty(waiverSets: WaiverSetContainerDTO[]): boolean {
        const waiverInfo = this._waiverDetailsBL.getCurrentWaiverInfo();
        return Boolean(waiverInfo);
    }

    /**
     * Error Handler - Centralized error processing and recovery
     *
     * This method handles all errors that occur during waiver data processing.
     * It logs errors for debugging, sets user-friendly error messages,
     * and returns a consistent error state structure.
     *
     * @param error - The error object from the failed operation
     * @returns Observable<PartyCardState> - Error state with user-friendly message
     */
    private handleError(error: any) {
        // Log error for debugging purposes
        console.error('Failed to load waiver sets:', error);

        // Create user-friendly error message
        const errorMessage =
            error.statusText ||
            'Failed to load waiver data. Please check your connection.';

        // Update error message in business logic service
        this._waiverDetailsBL.errorMessage.set(errorMessage);

        // Return consistent error state structure
        return of({
            loading: false,
            error: errorMessage,
            waiverSets: null,
            isSingleParty: false,
        } as PartyCardState);
    }

    /**
     * Loading State Generator - Creates initial loading state
     *
     * This method provides a consistent loading state structure that
     * indicates data is being fetched while maintaining the expected
     * state shape for consumers.
     *
     * @returns PartyCardState - Loading state with all required properties
     */
    private getLoadingState(): PartyCardState {
        return {
            loading: true,
            error: null,
            waiverSets: null,
            isSingleParty: false,
        };
    }
}
