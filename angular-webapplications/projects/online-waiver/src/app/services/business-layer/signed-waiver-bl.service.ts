/**
 * @fileoverview SignedWaiverBL is a business logic service for managing signed waivers
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-08-12
 */
/**
 * @service SignedWaiverBL
 * @description
 * Business logic service for managing signed waivers. This service extends RequestState
 * and handles the business logic for loading and managing customer signed waivers,
 * including API communication and state management.
 *
 * @usage
 * This service is used by components that need to load and manage signed waiver data,
 * providing a centralized business logic layer for signed waiver operations
 *
 * @inputs
 * - No inputs defined in this service
 *
 * @outputs
 * - signedWaivers: Signal containing array of CustomerSignedWaiverDTO objects
 * - loading: Signal indicating loading state
 * - errorMessage: Signal containing error messages
 *
 * @dependencies
 * - SignedWaiverDL: Data layer service for signed waiver API operations
 * - CookieService: Service for managing cookies
 * - RequestState: Base class for request state management
 * - CustomerSignedWaiverDTO: Data transfer object for signed waiver information
 *
 * @methods
 * - loadSignedWaivers(): Loads signed waivers for a specific customer
 */

import { inject, Injectable, signal } from '@angular/core';
import { SignedWaiverDL } from '../data-layer/signed-waiver-dl.service';
import { CookieService, RequestState } from 'lib-app-core';
import { CustomerSignedWaiverDTO } from '../../models/customer-signed-waiver-dto.model';

/**
 * Business logic service for managing signed waivers
 */
@Injectable()
export class SignedWaiverBL extends RequestState {
    private _signedWaiverDL = inject(SignedWaiverDL);
    private _cookieService = inject(CookieService)
    signedWaivers = signal<CustomerSignedWaiverDTO[]>([]);

    /**
     * Loads signed waivers for a specific customer by building API parameters,
     * invoking the Data Layer service, and managing the request state.
     */
    loadSignedWaivers() {
        const customerId = this._cookieService.getCookie('userId');
        this._signedWaiverDL.buildApiParams(customerId);
        const signedWaivers$ = this._signedWaiverDL.load();
        this._handleRequest(signedWaivers$, (response) => {
            this.signedWaivers.set(response.data || []);
        });
    }
}