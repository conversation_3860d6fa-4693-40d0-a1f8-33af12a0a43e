/**
 * @fileoverview Business layer service for handling waiver preview functionality
 * <AUTHOR>
 * @version 1.0.0
 */
import { inject, Injectable } from '@angular/core';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { RequestState } from 'lib-app-core';
import { WaiverPreviewData } from '../../interface/waiver.interface';

@Injectable()
export class WaiverPreviewBLService extends RequestState {
    private readonly _sanitizer = inject(DomSanitizer);

    /**
     * Creates a safe HTML data URL
     */
    createHtmlDataUrl(base64Data: string): SafeResourceUrl {
        const decodedHtml = atob(base64Data);
        const dataUrl = `data:text/html;charset=utf-8,${encodeURIComponent(
            decodedHtml
        )}`;
        return this._sanitizer.bypassSecurityTrustResourceUrl(dataUrl);
    }

    /**
     * Creates a safe PDF data URL
     */
    createPdfDataUrl(base64Data: string): SafeResourceUrl {
        const dataUrl = `data:application/pdf;base64,${base64Data}#toolbar=0&navpanes=0&zoom=90`;
        return this._sanitizer.bypassSecurityTrustResourceUrl(dataUrl);
    }

    /**
     * Creates an empty safe resource URL
     */
    createEmptyResourceUrl(): SafeResourceUrl {
        return this._sanitizer.bypassSecurityTrustResourceUrl('');
    }

    /**
     * Processes waiver data and returns the appropriate URL
     */
    processWaiverData(data: WaiverPreviewData | null): SafeResourceUrl {
        return data
            ? this.processValidData(data)
            : this.createEmptyResourceUrl();
    }

    /**
     * Processes valid waiver data
     */
    private processValidData(data: WaiverPreviewData): SafeResourceUrl {
        if (data.type === 'html') {
            return this.createHtmlDataUrl(data.base64Data);
        }

        if (data.type === 'pdf') {
            return this.createPdfDataUrl(data.base64Data);
        }

        return this.createEmptyResourceUrl();
    }
}
