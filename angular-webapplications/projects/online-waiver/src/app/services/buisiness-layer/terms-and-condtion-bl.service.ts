/**
 * @fileoverview Terms and conditions business layer service
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-08-05
*/
import { computed, inject, Injectable, signal } from '@angular/core';
import { Base64PdfBLService } from './base64-pdf-bl.service';
import { RequestState, RichContentContainerDTOModel, RichContentDTOModel, RichContentsServiceDL } from 'lib-app-core';

export interface TermsAndConditionsData {
    content: RichContentDTOModel | null;
    title: string;
}

@Injectable()
export class TermsAndConditionsBL extends RequestState {
    private readonly _base64PdfBLService = inject(Base64PdfBLService);
    private readonly _richContentsServiceDL = inject(RichContentsServiceDL);

    readonly showModal = signal(false);
    readonly modalContent = signal('');
    readonly modalTitle = signal('Terms and Conditions');
    readonly pdfBlobUrl = signal<string | null>(null);
    readonly richContents = signal<RichContentDTOModel[]>([]);

    readonly termsAndConditions = computed(() => {
        const richContents = this.richContents();
        return richContents.find(this.isPdfTermsAndConditions) || null;
    });

    readonly termsAndConditionsData = computed((): TermsAndConditionsData => {
        const content = this.termsAndConditions();
        return {
            content,
            title: content?.ContentName || 'Terms and Conditions',
        };
    });

    /**
     * Opens the terms and conditions modal with PDF content.
     *
     * This method performs the following operations:
     * 1. Retrieves the current terms and conditions data
     * 2. Cleans up any existing PDF blob URL to prevent memory leaks
     * 3. Converts the base64 PDF data to a blob URL for display
     * 4. Opens the modal with the PDF content or shows a fallback message
     *
     * If no content is available or PDF conversion fails, a fallback message is displayed.
     *
     * @returns {void} Nothing is returned
     */
    openTermsAndConditions(): void {
        const { content, title } = this.termsAndConditionsData();
        if (!content) return;

        this.cleanupPdfBlobUrl();

        const blobUrl = this._base64PdfBLService.convertBase64ToPdfUrl(
            content.Data
        );
        if (!blobUrl) {
            this.showFallback('Failed to convert PDF content.', title);
            return;
        }

        this.pdfBlobUrl.set(blobUrl);
        this.modalTitle.set(title);
        this.showModal.set(true);
    }

    /**
     * Closes the terms and conditions modal and cleans up resources.
     *
     * This method:
     * 1. Sets the modal visibility to false
     * 2. Cleans up the PDF blob URL to prevent memory leaks
     *
     * Should be called when the user closes the modal or navigates away.
     *
     * @returns {void} Nothing is returned
     */
    closeModal(): void {
        this.showModal.set(false);
        this.cleanupPdfBlobUrl();
    }

    /**
     * Shows a fallback message in the modal when PDF content cannot be displayed.
     *
     * This method is used as a fallback when:
     * - PDF conversion fails
     * - Content is not available
     * - Other display errors occur
     *
     * @param {string} message - The error message to display to the user
     * @param {string} title - The title to show in the modal header
     * @returns {void} Nothing is returned
     * @private
     */
    private showFallback(message: string, title: string): void {
        this.modalContent.set(message);
        this.modalTitle.set(title);
        this.showModal.set(true);
    }

    /**
     * Cleans up the current PDF blob URL to prevent memory leaks.
     *
     * This method:
     * 1. Retrieves the current blob URL from the signal
     * 2. Calls the cleanup service to revoke the blob URL
     * 3. Resets the blob URL signal to null
     *
     * Should be called before creating a new blob URL or when closing the modal.
     *
     * @returns {void} Nothing is returned
     * @private
     */
    private cleanupPdfBlobUrl(): void {
        const currentBlobUrl = this.pdfBlobUrl();
        if (currentBlobUrl) {
            this._base64PdfBLService.cleanupBlobUrl(currentBlobUrl);
            this.pdfBlobUrl.set(null);
        }
    }

    /**
     * Determines if a rich content item represents PDF terms and conditions.
     *
     * This method checks multiple criteria to identify terms and conditions PDFs:
     * - Content type includes 'application/pdf'
     * - File name ends with '.pdf'
     * - Content name contains both 'pdf' and ('terms' or 'conditions')
     *
     * The search is case-insensitive for better matching.
     *
     * @param {RichContentDTOModel} content - The rich content item to evaluate
     * @returns {boolean} True if the content is identified as PDF terms and conditions, false otherwise
     * @private
     */
    private isPdfTermsAndConditions(content: RichContentDTOModel): boolean {
        const name = content.ContentName?.toLowerCase() || '';
        const type = content.ContentType?.toLowerCase() || '';
        const file = content.FileName?.toLowerCase() || '';

        return (
            type.includes('application/pdf') ||
            file.endsWith('.pdf') ||
            (name.includes('pdf') &&
                (name.includes('terms') || name.includes('conditions')))
        );
    }

    /**
     * Cleans up a blob URL to prevent memory leaks.
     *
     * This method provides backward compatibility for external cleanup calls.
     * It delegates the cleanup to the base64 PDF service.
     *
     * @param {string | null} blobUrl - The blob URL to clean up, or null if no cleanup needed
     * @returns {void} Nothing is returned
     */
    cleanupBlobUrl(blobUrl: string | null): void {
        this._base64PdfBLService.cleanupBlobUrl(blobUrl);
    }

    loadRichContents(): void {
        this._handleRequest(
            this._richContentsServiceDL.load(),
            (response: RichContentContainerDTOModel) => {
                this.richContents.set(response.data);
            }
        );
    }
}
