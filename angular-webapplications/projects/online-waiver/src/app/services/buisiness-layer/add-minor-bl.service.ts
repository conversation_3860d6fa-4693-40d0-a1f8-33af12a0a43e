/**
 * @fileoverview Add Minor Business Layer Service
 * <AUTHOR>
 * @created 2025-08-14
 * @description This service is used to add minor to the primary account
 * @version 1.0.0
 */
import { inject, Injectable, signal } from '@angular/core';
import { FormArray, FormGroup } from '@angular/forms';
import {
    AddMinorServiceDL,
    CustomerUIMetadataContainerDTOModel,
    RequestState,
} from 'lib-app-core';
import { IParticipant } from '../../interface/customer.interface';
import { CreateCustomerPayloadBuilderService } from './create-customer-payload-builder.service';

@Injectable()
export class AddMinorServiceBL extends RequestState {
    private readonly _createCustomerPayloadBuilderService = inject(
        CreateCustomerPayloadBuilderService
    );
    private readonly _addMinorDLService = inject(AddMinorServiceDL);
    readonly showAddParticipantModal = signal<boolean>(false);
    readonly addMinorSuccess = signal<boolean>(false);

    // TODO: Remove hardcoded values
    addMinors(
        form: FormArray<FormGroup>,
        primaryAccountData: IParticipant,
        customerUIMetadata: CustomerUIMetadataContainerDTOModel[]
    ): void {
        const customerDTO =
            this._createCustomerPayloadBuilderService.buildCustomerPayloadFromFormData(
                {
                    EMAIL: primaryAccountData.email,
                    CONTACT_PHONE: primaryAccountData.phoneNumber,
                    minors: form.value,
                },
                customerUIMetadata
            );

        const [minorCustomerDTO] = customerDTO.map(
            (item) => item.CustomerRelationshipDTOList
        );
        this._addMinorDLService.buildApiParams({
            customerId: primaryAccountData.id,
        });
        this._addMinorDLService.buildApiPayload(minorCustomerDTO);
        this._handleRequest(
            this._addMinorDLService.load(),
            () => {
                this.addMinorSuccess.set(true);
                this.showAddParticipantModal.set(false);
            },
            () => {
                this.addMinorSuccess.set(false);
            }
        );
    }
}
