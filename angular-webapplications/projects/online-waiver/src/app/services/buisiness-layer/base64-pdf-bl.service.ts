/**
 * @fileoverview Base64 PDF business layer service
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-08-05
*/
import { Injectable, inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';

export interface PdfConversionResult {
    blobUrl: string;
    blob: Blob;
}

@Injectable()
export class Base64PdfBLService {
    private readonly platformId = inject(PLATFORM_ID);
    readonly PDF_CONTENT_TYPE = 'pdf';
    readonly PDF_DATA_URL_PREFIX = /^data:application\/pdf;base64,/;
    readonly BASE64_CHUNK_SIZE = 512;

    /**
     * Validates the PDF content before processing
     * @param base64 - The base64 string to validate
     * @returns boolean
     */
    validatePdfContent(base64: string): boolean {
        if (!base64) return false;

        // Remove the data URL prefix and any whitespace
        const cleanBase64 = base64
            .replace(this.PDF_DATA_URL_PREFIX, '')
            .replace(/\s/g, '');

        try {
            if (isPlatformBrowser(this.platformId)) {
                atob(cleanBase64);
            } else {
                Buffer.from(cleanBase64, 'base64');
            }
            return true;
        } catch {
            return false;
        }
    }

    /**
     * Converts base64 string to Blob (safe for browser)
     * @param base64 - The base64 string to convert to Blob
     * @returns Blob
     */
    convertBase64ToBlob(base64: string): Blob {
        const cleanBase64 = base64.replace(this.PDF_DATA_URL_PREFIX, '');

        let binaryString: string;

        if (isPlatformBrowser(this.platformId)) {
            binaryString = atob(cleanBase64);
        } else {
            const buffer = Buffer.from(cleanBase64, 'base64');
            binaryString = String.fromCharCode(...buffer);
        }

        const byteArrays = [];
        for (let offset = 0; offset < binaryString.length; offset += this.BASE64_CHUNK_SIZE) {
            const slice = binaryString.slice(offset, offset + this.BASE64_CHUNK_SIZE);
            const byteNumbers = Array.from(slice, c => c.charCodeAt(0));
            byteArrays.push(new Uint8Array(byteNumbers));
        }

        return new Blob(byteArrays, { type: 'application/pdf' });
    }

    /**
     * Converts base64 PDF to a secure blob URL
     * @param base64 - The base64 string to convert to Blob
     * @returns Blob
     */
    convertBase64ToPdfUrl(base64: string): string | null {
        if (!this.validatePdfContent(base64)) {
            return null;
        }

        const blob = this.convertBase64ToBlob(base64);
        return `${URL.createObjectURL(blob)}#toolbar=0&navpanes=0&zoom=90`;
    }

    /**
     * Clean up blob URL
     * @param blobUrl - The blob URL to clean up
     */
    cleanupBlobUrl(blobUrl: string | null): void {
        if (blobUrl) {
            URL.revokeObjectURL(blobUrl);
        }
    }
}
