/**
 * @fileoverview
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-02
 */

import { mergeApplicationConfig, ApplicationConfig, APP_BOOTSTRAP_LISTENER, inject, TransferState, provideAppInitializer } from '@angular/core';
import { provideServerRendering } from '@angular/platform-server';
import { appConfig } from './app.config';
import { provideHttpClient, withFetch } from '@angular/common/http';
import { transferEnvVars } from 'lib/lib-app-core/src/lib/initializers/transfer-state';
import { AppInitService } from './core/app-init.service';

import * as dotenv from 'dotenv';
// import { fileURLToPath } from 'node:url';
// import { dirname, resolve } from 'node:path';

// // Convert `import.meta.url` to a directory path
// const __filename = fileURLToPath(import.meta.url);
// const __dirname = dirname(__filename);

// // Load the correct .env file dynamically
// dotenv.config({ path: resolve(__dirname, '../../config/.env') });
dotenv.config({ path: './dist/config/.env' });

// require('dotenv').config();
console.log(require('path').resolve('./dist/config/.env'));


const serverConfig: ApplicationConfig = {
    providers: [
        provideServerRendering(),
        provideHttpClient(withFetch()),
        provideAppInitializer(transferEnvVars),

        {
            provide: APP_BOOTSTRAP_LISTENER,
            useFactory: () => {
                const appInitService = inject(AppInitService);
                return async () => {
                    await appInitService.loadInitialData(); // 🟢 Call multiple APIs before bootstrapping
                };
            },
            multi: true,
        },
    ]
};

export const config = mergeApplicationConfig(appConfig, serverConfig);
