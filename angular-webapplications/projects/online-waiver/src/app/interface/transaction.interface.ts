/**
 * @fileoverview Transaction-related interfaces for the online waiver system
 * @description This file contains TypeScript interfaces that define the data structures
 *              for transactions, waiver signing, and booking operations in the system.
 *              These interfaces are used throughout the application for type safety
 *              and data consistency.
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-08-18
 */

/**
 * WaiverSignedDTO - Represents a signed waiver record
 *
 * This interface defines the structure for waiver signing data, including
 * customer information, waiver details, and audit trail information.
 * Used when customers sign waivers for activities or services.
 */
export interface WaiverSignedDTO {
    WaiverSignedId: number;
    WaiverSetId: number;
    WaiverSetDetailId: number;
    TrxId: number;
    LineId: number;
    SignedMode: string;
    UserId: number;
    IsActive: boolean;
    LastUpdatedDate: string;
    LastUpdatedBy: string;
    GUID: string;
    Site_id: number;
    SynchStatus: boolean;
    MasterEntityId: number;
    ProductId: number;
    ProductName: string;
    WaiverFile: string | null;
    Guid: string;
    CreationDate: string;
    CreatedBy: string;
    CustomerSignedWaiverId: number;
    CustomerSignedWaiverFileName: string;
    WaiverSignedDate: string;
    IsOverriden: boolean;
    CustomerId: number;
    IsChanged: boolean;
}

/**
 * TransactionLineDTO - Represents a line item within a transaction
 *
 * This interface defines individual items within a transaction, including
 * products, pricing, taxes, and various business logic flags. Each line
 * represents a specific product or service being purchased.
 */
export interface TransactionLineDTO {
    taxAmount: number;
    taxName: string;
    TransactionId: number;
    LineId: number;
    ApprovedBy: string;
    ProductId: number;
    Price: number;
    Quantity: number;
    Amount: number;
    CardId: number;
    CardNumber: string | null;
    Credits: number | null;
    Courtesy: number | null;
    TaxPercentage: number;
    TaxId: number;
    Time: string | null;
    Bonus: number | null;
    Tickets: number | null;
    LoyaltyPoints: number | null;
    Remarks: string;
    PromotionId: number;
    ReceiptPrinted: boolean;
    ParentLineId: number;
    UserPrice: boolean;
    KOTPrintCount: number;
    GameplayId: number;
    KDSSent: boolean;
    CreditPlusConsumptionId: number;
    CancelledTime: string | null;
    CancelledBy: string | null;
    ProductDescription: string | null;
    IsWaiverSignRequired: string;
    OriginalLineId: number;
    Guid: string;
    ClientGuid: string;
    SynchStatus: boolean;
    SiteId: number;
    MasterEntityId: number;
    MembershipId: number;
    MembershipRewardsId: number;
    ExpireWithMembership: string;
    ForMembershipOnly: string;
    IsChanged: boolean;
    ProductDetail: string | null;
    ProductName: string;
    ProductTypeCode: string;
    Selected: boolean;
    CreatedBy: string | null;
    CreationDate: string;
    LastUpdatedBy: string | null;
    LastUpdatedDate: string;
    CardGuid: string;
    ProductsDTO: any | null;
    WaiverSignedDTOList: WaiverSignedDTO[];
    AttractionBookingDTO: any | null;
    ParentLineGuid: string;
    TransactionDiscountsDTOList: any | null;
    KDSOrderLineDTOList: any | null;
    CancelCode: string;
    TaxAmount: number;
    TaxName: string;
    SubscriptionHeaderDTO: any | null;
    AllocatedProductAmount: number | null;
    ExchangeFromTrxId: number;
    ExchangeFromLineId: number;
}

/**
 * TransactionDTO - Represents a complete transaction
 *
 * This interface defines the main transaction structure containing
 * header information, payment details, customer data, and associated
 * line items. This is the primary data structure for financial transactions.
 */
export interface TransactionDTO {
    TransactionId: number;
    TransactionDate: string;
    TransactionAmount: number;
    TransactionDiscountPercentage: number;
    TransactionDiscountAmount: number;
    TaxAmount: number;
    TransactionNetAmount: number;
    PosMachine: string;
    UserId: number;
    PaymentMode: number;
    PaymentModeName: string | null;
    CashAmount: number;
    CreditCardAmount: number;
    GameCardAmount: number;
    PaymentReference: string;
    PrimaryCardId: number;
    OrderId: number;
    POSTypeId: number;
    TransactionNumber: string;
    TransactionOTP: string;
    Remarks: string;
    POSMachineId: number;
    OtherPaymentModeAmount: number;
    Status: string;
    TransactionProfileId: number;
    LastUpdateTime: string;
    LastUpdatedBy: string;
    TokenNumber: string;
    OriginalSystemReference: string;
    CustomerId: number;
    ExternalSystemReference: string;
    ReprintCount: number;
    OriginalTransactionId: number;
    OrderTypeGroupId: number;
    TableNumber: string;
    Paid: number;
    UserName: string;
    CreatedBy: number;
    CreationDate: string;
    Guid: string;
    SynchStatus: boolean;
    SiteId: number;
    MasterEntityId: number;
    Selected: boolean;
    Tickets: any | null;
    Receipt: any | null;
    TicketsHTML: any | null;
    ReceiptHTML: any | null;
    VisitDate: string | null;
    ApplyVisitDate: boolean;
    IsChanged: boolean;
    IsChangedRecursive: boolean;
    TransactionLinesDTOList: TransactionLineDTO[];
    TrxPaymentsDTOList: any[];
    DiscountsSummaryDTOList: any | null;
    DiscountApplicationHistoryDTOList: any | null;
    PrimaryCard: string;
    ReceiptDTO: any | null;
    TicketDTOList: any | null;
    TicketPrinterMapDTOList: any | null;
    CustomerName: string;
    CommitTransaction: boolean;
    SaveTransaction: boolean;
    CloseTransaction: boolean;
    ApplyOffset: boolean;
    PaymentProcessingCompleted: boolean;
    ReverseTransaction: boolean;
    CustomerIdentifier: string;
    GuestName: string;
    TrxPOSPrinterOverrideRulesDTO: any | null;
    TransctionOrderDispensingDTO: any | null;
}

/**
 * TransactionResponse - API response wrapper for transaction data
 *
 * This interface defines the structure of API responses that return
 * transaction data, including pagination information and authentication tokens.
 */
export interface TransactionResponse {
    data: TransactionDTO[];
    currentPageNo: number;
    totalCount: number;
    token: string;
}

/**
 * TransactionTimeParams - Parameters for time-based transaction queries
 *
 * This interface defines parameters used when querying transactions
 * based on time or booking information.
 */
export interface TransactionTimeParams {
    transactionId: number;
}

/**
 * TransactionTimeDTO - Represents booking and time-based transaction data
 *
 * This interface defines the structure for booking information, including
 * reservation details, customer information, facility data, and scheduling
 * information. Used for time-based activities and reservations.
 */
export interface TransactionTimeDTO {
    BookingId: number;
    BookingClassId: number;
    BookingName: string;
    FromDate: string;
    RecurFlag: string;
    RecurFrequency: string;
    RecurEndDate: string | null;
    Quantity: number;
    ReservationCode: string;
    Status: string;
    CardId: number;
    CardNumber: string;
    CustomerId: number;
    CustomerName: string;
    ExpiryTime: string | null;
    Channel: string;
    Remarks: string;
    ContactNo: string;
    AlternateContactNo: string;
    Email: string;
    IsEmailSent: number;
    ToDate: string;
    TrxId: number;
    TrxNumber: string;
    TrxStatus: string;
    TrxNetAmount: number;
    Age: number;
    Gender: string;
    PostalAddress: string;
    BookingProductId: number;
    BookingProductName: string;
    FacilityName: string;
    FacilityMapId: number;
    FacilityMapName: string;
    AttractionScheduleId: number;
    ExtraGuests: number;
    CreatedBy: string;
    CreationDate: string;
    LastUpdatedBy: string;
    LastupdateDate: string;
    Guid: string;
    SiteId: number;
    SynchStatus: boolean;
    MasterEntityId: number;
    BookingCheckListDTOList: any[] | null;
    ServiceChargeAmount: number;
    ServiceChargePercentage: number;
    IsChanged: boolean;
}

/**
 * TransactionTimeResponse - API response wrapper for time-based transaction data
 *
 * This interface defines the structure of API responses that return
 * time-based transaction or booking data.
 */
export interface TransactionTimeResponse {
    data: TransactionTimeDTO[];
}

/**
 * TransactionTimeParams - Parameters for time-based transaction queries
 *
 * This interface defines parameters used when querying time-based
 * transactions or booking information.
 */
export interface TransactionTimeParams {
    transactionId: number;
}
