import { WaiverSetContainerDTO } from './waiver.interface';

export interface IPartyReservationQueryParams {
    reservationCode?: string;
}
export interface GuidParams {
    guid: string;
}

export interface GetAllWaiverSetParams {
    siteId: string;
    languageId: string;
}
export interface PartyCardState {
    loading: boolean;
    error: string | null;
    waiverSets: WaiverSetContainerDTO[] | null;
    isSingleParty: boolean;
}
