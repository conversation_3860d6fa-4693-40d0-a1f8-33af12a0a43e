import { Injectable } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { BehaviorSubject, Observable } from 'rxjs';
import { filter } from 'rxjs/operators';
import { TitleCasePipe } from '@angular/common';
import { Breadcrumb } from '../bread-crumbs/bread-crumbs.component';

@Injectable({ providedIn: 'root' })
export class BreadCrumbService {
    private readonly breadcrumbSubject = new BehaviorSubject<Breadcrumb[]>([]);
    readonly breadcrumbs$: Observable<Breadcrumb[]> =
        this.breadcrumbSubject.asObservable();
    private readonly titleCasePipe = new TitleCasePipe();

    constructor(private readonly router: Router) {}

    initialize(route: ActivatedRoute): void {
        this.router.events
            .pipe(filter((event) => event instanceof NavigationEnd))
            .subscribe(() => this.updateBreadcrumbs(route));

        this.updateBreadcrumbs(route);
    }

    // Method to manually update breadcrumbs
    refreshBreadcrumbs(route: ActivatedRoute): void {
        this.updateBreadcrumbs(route);
    }

    private updateBreadcrumbs(route: ActivatedRoute): void {
        this.breadcrumbSubject.next(this.buildBreadcrumbs(route.root));
    }

    private buildBreadcrumbs(
        route: ActivatedRoute,
        url = '',
        breadcrumbs: Breadcrumb[] = []
    ): Breadcrumb[] {
        for (const child of route.children) {
            const routeURL =
                child.snapshot?.url.map((segment) => segment.path).join('/') ||
                '';
            if (!routeURL) continue;

            const fullUrl = `${url}/${routeURL}`;
            const label = child.snapshot?.data['breadcrumb'] || routeURL;
            breadcrumbs.push({
                label: this.titleCasePipe.transform(label),
                link: fullUrl,
            });

            this.buildBreadcrumbs(child, fullUrl, breadcrumbs);
        }
        return breadcrumbs;
    }
}
