<header class="sticky top-0 w-full bg-surface-white drop-shadow-sm z-50">
    <!-- Desktop Header -->
    <div class="hidden lg:flex justify-center items-center px-6 lg:px-10 py-4 w-full">
        <div class="flex items-center gap-5">
            <a routerLink="/" class="flex items-center">
                <img src="assets/icons/semnox-logo.svg" alt="Semnox Logo" class="min-w-[101px]" />
            </a>

            <!-- Search Bar -->
            <div class="max-w-52 relative">
                <lib-text-input placeholder="Search" [icon]="'/assets/icons/magnifier.svg'"
                    [iconAlt]="'Magnifier'"></lib-text-input>
            </div>
        </div>

        <div class="flex items-center justify-center space-x-6">
            <nav class="flex items-center justify-between space-x-6">
                @for (item of navItems; track $index) {
                <ng-container>
                    <a [routerLink]="item.link" routerLinkActive="text-secondary-blue font-medium"
                        class="flex items-center gap-2 text-xs xl:text-sm text-primary hover:text-secondary-blue">
                        <img [src]="item.icon" [alt]="item.label" class="w-4 h-4 xl:w-5 xl:h-5" />
                        <span> {{ item.label }}</span>
                    </a>
                </ng-container>
                }
            </nav>

            <!-- My Account -->
            <app-dropdown-menu [items]="accountMenuItems" [alignRight]="true">
                <button triggerButton
                    class="flex items-center text-xs xl:text-sm text-primary hover:text-secondary-blue">
                    <img src="/assets/icons/account.svg" alt="Account" class="w-4 h-4 xl:w-5 xl:h-5 mr-2" />
                    <span i18n="header.style-1.my-account" class="text-nowrap">My Account</span>
                    <img src="/assets/icons/chevron-down.svg" alt="Dropdown" class="w-4 h-4 xl:w-5 xl:h-5 ml-2" />
                </button>
            </app-dropdown-menu>

            <!-- Shopping Cart -->
            <a href="#" class="text-primary">
                <img src="/assets/icons/cart.svg" alt="Cart" class="w-5 h-5 xl:w-6 xl:h-6 mx-6 xl:mx-0" />
            </a>

            <!-- Language Selector -->
            <app-language-selection />
        </div>
    </div>

    <!-- Mobile Header -->
    <div class="lg:hidden flex items-center justify-between px-6 pr-4 py-4">
        <div class="flex items-center gap-5">
            <button class="text-primary focus:outline-none min-h-6 min-w-6"
                (click)="toggleMobileSidebar(); $event.stopPropagation()" aria-label="Toggle menu">
                @if (!mobileSidebarOpen) {
                <img src="assets/icons/hamburger.svg" alt="open menu" />
                } @else {
                <img src="/assets/icons/menu-close.svg" alt="close menu" />
                }
            </button>

            <a routerLink="/" class="flex items-center">
                <img src="assets/icons/semnox-logo.svg" alt="Semnox Logo" />
            </a>
        </div>

        <div class="flex items-center gap-3">
            <!-- Shopping Cart -->
            <a href="#" class="text-primary">
                <img src="/assets/icons/cart.svg" alt="Cart" class="w-6 h-6" />
            </a>

            <!-- Language Selector -->
            <app-language-selection />
        </div>
    </div>
</header>

<!-- Mobile Sidebar -->

<app-mobile-sidebar [isOpen]="mobileSidebarOpen" (closeSidebar)="closeSideBar()"></app-mobile-sidebar>