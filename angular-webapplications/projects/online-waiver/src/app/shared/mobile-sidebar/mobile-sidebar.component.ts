import { CommonModule, isPlatformBrowser } from '@angular/common';
import {
    Component,
    HostListener,
    Inject,
    input,
    output,
    PLATFORM_ID,
    inject,
} from '@angular/core';
import { RouterLink, RouterLinkActive } from '@angular/router';
import { AuthService } from '../../core/auth.service';
interface MenuItem {
    label: string;
    icon: string;
    link?: string;
    action?: () => void;
    children?: MenuItem[];
    isExpanded?: boolean;
}

@Component({
    selector: 'app-mobile-sidebar',
    imports: [CommonModule, RouterLink, RouterLinkActive],
    templateUrl: './mobile-sidebar.component.html',
    styleUrl: './mobile-sidebar.component.scss',
})
export class MobileSidebarComponent {
    isOpen = input(false);
    closeSidebar = output<void>();
    private readonly MOBILE_BREAKPOINT = 1024;

    isBrowser = false;
    private authService = inject(AuthService);

    constructor(@Inject(PLATFORM_ID) private platformId: Object) {
        this.isBrowser = isPlatformBrowser(this.platformId);
    }

    // HostListener for window resize
    @HostListener('window:resize', ['$event'])
    onResize() {
        if (this.isBrowser) {
            if (window.innerWidth < this.MOBILE_BREAKPOINT && this.isOpen()) {
                this.closeSidebar.emit();
            }
        }
    }

    menuItems: MenuItem[] = [
        {
            label: $localize`:mobile-sidebar.home@@mobile-sidebar.home:Home`,
            icon: '/assets/icons/home.svg',
            link: '/home',
        },
        {
            label: $localize`:mobile-sidebar.cards@@mobile-sidebar.cards:Cards`,
            icon: '/assets/icons/card.svg',
            link: '/cards',
        },
        {
            label: $localize`:mobile-sidebar.waivers@@mobile-sidebar.waivers:Waivers`,
            icon: '/assets/icons/preview-blue.svg',
            link: '/waivers',
            isExpanded: false,
            children: [
                {
                    label: $localize`:mobile-sidebar.sign-waiver@@mobile-sidebar.sign-waiver:Sign Waiver`,
                    icon: '',
                    link: '/waivers/sign-waiver',
                },
                {
                    label: $localize`:mobile-sidebar.view-signed-waivers@@mobile-sidebar.view-signed-waivers:View Signed Waivers`,
                    icon: '',
                    link: '/waivers/my-signed-waivers',
                },
                {
                    label: $localize`:mobile-sidebar.view-relations@@mobile-sidebar.view-relations:View Relations`,
                    icon: '',
                    link: '/waivers/my-relations',
                },
            ],
        },
        {
            label: $localize`:mobile-sidebar.food-and-beverages@@mobile-sidebar.food-and-beverages:Food & Beverages`,
            icon: '/assets/icons/food-beverages.svg',
            link: '/f-and-b',
        },
        {
            label: $localize`:mobile-sidebar.recharge@@mobile-sidebar.recharge:Recharge`,
            icon: '/assets/icons/recharge.svg',
            link: '/recharge',
        },
        {
            label: $localize`:mobile-sidebar.party-reservations@@mobile-sidebar.party-reservations:Party Reservations`,
            icon: '/assets/icons/reservation.svg',
            link: '/reservations',
        },
        {
            label: $localize`:mobile-sidebar.my-account@@mobile-sidebar.my-account:My Account`,
            icon: '/assets/icons/account.svg',
            isExpanded: true,
            children: [
                {
                    label: $localize`:mobile-sidebar.my-profile@@mobile-sidebar.my-profile:My Profile`,
                    icon: '',
                    link: 'my-accounts/my-profile',
                },
                { label: $localize`:mobile-sidebar.my-cards@@mobile-sidebar.my-cards:My Cards`, icon: '', link: 'my-accounts/my-cards' },
                {
                    label: $localize`:mobile-sidebar.my-subscriptions@@mobile-sidebar.my-subscriptions:My Subscriptions`,
                    icon: '',
                    link: 'my-accounts/my-subscriptions',
                },
                { label: $localize`:mobile-sidebar.my-orders@@mobile-sidebar.my-orders:My Orders`, icon: '', link: 'my-accounts/my-orders' },
                {
                    label: $localize`:mobile-sidebar.change-password@@mobile-sidebar.change-password:Change Password`,
                    icon: '',
                    link: 'my-accounts/change-password',
                },
            ],
        },
    ];

    toggleSubmenu(item: MenuItem): void {
        item.isExpanded = !item.isExpanded;
    }

    closeMobileSidebar(): void {
        if (this.isOpen()) {
            this.closeSidebar.emit();
        }
    }

    logout(): void {
        // Use the shared auth service
        this.authService.logout();

        // Close the mobile sidebar if it's open
        if (this.isOpen()) {
            this.closeSidebar.emit();
        }
    }
}
