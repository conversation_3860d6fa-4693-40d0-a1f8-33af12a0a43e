/**
 * @fileoverview
 *
 * <AUTHOR>
 * @version 1.0.0
 */

import { isPlatformBrowser, isPlatformServer } from '@angular/common';
import { Injectable, PLATFORM_ID, inject } from '@angular/core';
import { Router } from '@angular/router';
import {
    AuthenticateSystemUsersServiceDL,
    CookieService,
    DEFAULT_APP_CONFIG_TOKEN,
    SiteContextService,
    SiteViewsServiceDL,
    areEqual,
} from 'lib-app-core';
import { firstValueFrom } from 'rxjs';
import { AuthenticateSystemUsersDTOModel } from '../models/authenticate-system-users-dto.model';
import { AuthenticateSystemUsersServerServiceDL } from './data-layer/authenticate-system-users-server-dl.service';

@Injectable({
    providedIn: 'root',
})
export abstract class AppInitBaseService {
    private _defaultAppConfig = inject(DEFAULT_APP_CONFIG_TOKEN);
    private _authenticateS<PERSON><PERSON><PERSON>sersServerServiceDL = inject(
        AuthenticateSystemUsersServerServiceDL
    );
    private _authenticateSystemUsersServiceDL = inject(
        AuthenticateSystemUsersServiceDL
    );
    private _siteViewsServiceDL = inject(SiteViewsServiceDL);
    private _siteContextService = inject(SiteContextService);
    private _platformId = inject(PLATFORM_ID);
    private _cookieService = inject(CookieService);
    private _webApiToken: string | null = null;
    private _router = inject(Router);
    protected _siteId: number = this._siteContextService.siteId;
    protected _languageId: string = this._siteContextService.languageId;
    protected _machineName: number = this._defaultAppConfig['machineName'];

    protected abstract getAppSpecificApiData(): any;

    constructor() {}

    async handleSiteInitialization() {
        const isMasterSite =
            this._siteContextService.selectedLocation?.IsMasterSite;
        if (isMasterSite) {
            this.navigateToSiteSelection();
        }
    }

    async loadInitialData() {
        try {
            // Step 1: Authenticate user
            if (isPlatformServer(this._platformId))
                await this.authenticateUserServer();
            else await this.authenticateUser();
            // Step 2: Fetch required API data with new token
            await this.fetchApiData();
        } catch (error) {
            console.error('Failed to load initial data', error);
        }
    }

    private async authenticateUser() {
        const loginPayload = {
            machineName: this._machineName,
            LanguageId: this._languageId,
            siteId: this._siteId,
        };

        this._authenticateSystemUsersServiceDL.buildApiParams(loginPayload);
        await firstValueFrom(this._authenticateSystemUsersServiceDL.load());

        this._authenticateSystemUsersServiceDL.subscribeToData(
            (data: AuthenticateSystemUsersDTOModel) => {
                this.storeWebApiToken(data.WebApiToken);
            }
        );
    }

    private storeWebApiToken(token: string) {
        this._webApiToken = token;
        this._cookieService.setCookie('webApiToken', this._webApiToken);
    }

    private async authenticateUserServer() {
        const loginPayload = {
            machineName: this._machineName,
            LanguageId: this._languageId,
            siteId: this._siteId,
            LoginId: process.env['LOGIN_ID'],
            Password: process.env['PASSWORD'],
        };

        this._authenticateSystemUsersServerServiceDL.buildApiParams(
            loginPayload
        );
        await firstValueFrom(
            this._authenticateSystemUsersServerServiceDL.load()
        );

        this._authenticateSystemUsersServerServiceDL.subscribeToData(
            (data: AuthenticateSystemUsersDTOModel) => {
                this.storeWebApiToken(data.WebApiToken);
            }
        );
    }

    private async fetchApiData() {
        if (!this.webApiToken$) {
            throw new Error('Missing API token');
        }

        await firstValueFrom(this._siteViewsServiceDL.load());

        this._siteContextService.languageId = this._languageId;
        this._siteContextService.siteId = this.siteId$;
    }

    navigateToSiteSelection() {
        if (isPlatformBrowser(this._platformId)) {
            this._router.navigate(['/site-selection']);
        }
    }

    async updateSiteId(newSiteId: number) {
        if (!areEqual(newSiteId, this._siteId)) {
            this.siteId$ = newSiteId;
            await this.loadInitialData();
        }
    }

    async updateLanguageId(newLanguageId: string) {
        if (!areEqual(newLanguageId, this._languageId)) {
            this.languageId$ = newLanguageId;
            await this.loadInitialData();
        }
    }

    set siteId$(value: number) {
        if (value) {
            this._siteId = value;
            this._cookieService.setCookie('siteId', value.toString());
            this._siteContextService.siteId = value;
        }
    }

    get siteId$(): number {
        return this._siteId;
    }

    set webApiToken$(value: string | null) {
        if (value) this._webApiToken = value.trim();
    }

    get webApiToken$(): string | null {
        return this._webApiToken;
    }

    get languageId$(): string {
        return this._languageId;
    }

    set languageId$(value: string) {
        if (value) {
            this._languageId = value;
            this._siteContextService.languageId = value;
        }
    }
}
