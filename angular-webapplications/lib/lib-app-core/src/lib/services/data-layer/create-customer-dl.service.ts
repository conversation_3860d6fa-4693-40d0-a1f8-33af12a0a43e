/**
 * @fileoverview Create customer data layer service
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-08-05
 */
import { Injectable } from "@angular/core";
import { ApiServiceBase } from "./api-service-base-dl.service";
import { appCoreConstant, CreateCustomerResponseDTOModel } from "lib-app-core";
import { Observable } from "rxjs";

@Injectable()
export class CreateCustomerServiceDL extends ApiServiceBase {
    private _customerPayload: any;
   
    constructor() {
        super(appCoreConstant.TRANSFER_STATE_KEYS.CREATE_CUSTOMER_DATA, 'CREATE_CUSTOMER');
        this.init();
    }

    set customerPayload(customer: any) {
        this._customerPayload = customer;
    }

    load(): Observable<{ data: [CreateCustomerResponseDTOModel] }> {
        const url = this.getApiUrl();
        return this._http.post<{ data: [CreateCustomerResponseDTOModel] }>(url, this._customerPayload);
    }
}