/**
 * @fileoverview
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

import { Injectable } from '@angular/core';
import { Observable, tap } from 'rxjs';
import { ApiServiceBase } from './api-service-base-dl.service';
import { LanguageContainerDTOModel } from '../../models/language-container-dto.model';
import { appCoreConstant, extractDTOList } from 'lib-app-core';

export type LanguageContainerApiParams = {
    siteId: number
}
@Injectable({ providedIn: 'root'})
export class LanguageContainerServiceDL extends ApiServiceBase {
    private _apiData: LanguageContainerDTOModel[] = [];
    private _apiParams!: LanguageContainerApiParams;
    constructor() {
        super(appCoreConstant.TRANSFER_STATE_KEYS.LANGUAGE_CONTAINER_DATA, 'LANGUAGE_CONTAINER_JSON')
        this.init();
    }

    buildApiParams(data: LanguageContainerApiParams) {
        this._apiParams = data
    }

    load(): Observable<{ data: LanguageContainerDTOModel[] }> {
        const url = this.getJsonApiUrl(this._apiParams);
        return this._http.get<{ data: LanguageContainerDTOModel[] }>(url).pipe(
            tap(response => {
                const dtoList = extractDTOList<LanguageContainerDTOModel>(response, appCoreConstant.LANGUAGE_CONTAINER_DTO_LIST);

                this._apiData = LanguageContainerDTOModel.fromList(dtoList);
                this.storeData(this._apiData);
            })
        );
    }

    // getLanguageList(): LanguageContainerDTOModel[] {
    //     return this._apiData;
    // }

    // getByLanguageId(id: number): LanguageContainerDTOModel | undefined {
    //     return this._apiData?.find((lang: any) => lang.LanguageId === id);
    // }    

    // getByLanguageCode(code: string): LanguageContainerDTOModel | undefined {
    //     return this._apiData?.find((lang: any) => lang.LanguageCode === code);
    // }

}

/**
 Usage:
 this.languageContainerService.subscribeToData(data => {
    this.localVar = data;
});

 */