/**
 * @fileoverview Data layer service for handling transaction time API calls
 * @description This service extends the base API service to handle transaction time
 *              operations. It provides methods to fetch transaction time information
 *              using transaction ID parameters and manages the API communication
 *              for time-based transaction data.
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-01-27
 */

import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiServiceBase } from './api-service-base-dl.service';
import {
    TransactionTimeParams,
    TransactionTimeResponse,
} from '../../interface/transaction.interface';

/**
 * TransactionTimeDL - Data Layer Service for Transaction Time
 *
 * This service handles all API operations related to transaction time data.
 * It extends the base API service and provides specific functionality for:
 * - Fetching transaction time information by transaction ID
 * - Building API parameters for time-based requests
 * - Managing time-related transaction data communication
 *
 * The service follows the data layer pattern and is responsible for:
 * - API endpoint configuration
 * - Parameter building and validation
 * - HTTP request handling
 * - Response type safety for time-based data
 */
@Injectable({ providedIn: 'root' })
export class TransactionTimeDL extends ApiServiceBase {
    /**
     * API parameters for transaction time request
     * Stores the transaction ID and other parameters needed for the API call
     */
    private _apiParams!: TransactionTimeParams;

    /**
     * Constructor - Initializes the service with endpoint configuration
     *
     * Sets up the service with the 'transaction_time' endpoint and
     * 'GET_TRANSACTION_TIME' operation identifier.
     */
    constructor() {
        super('transaction_time', 'GET_TRANSACTION_TIME');
        this.init();
    }

    /**
     * Builds API parameters for the transaction time request
     *
     * This method is called by the business layer to set the parameters
     * before making the API call. It stores the transaction ID and other
     * required parameters for the transaction time request.
     *
     * @param data - TransactionTimeParams object containing the transaction ID
     */
    buildApiParams(data: TransactionTimeParams) {
        this._apiParams = data;
    }

    /**
     * Loads transaction time data from the API
     *
     * This method constructs the API URL with the provided transaction ID and
     * makes an HTTP GET request to fetch transaction time information.
     * The response includes time-based transaction data such as booking details,
     * scheduling information, and time-related metadata.
     *
     * @returns Observable<TransactionTimeResponse> - Transaction time response
     */
    load(): Observable<TransactionTimeResponse> {
        const { transactionId } = this._apiParams;
        const url = this.getApiUrl().replace(
            '{transactionId}',
            transactionId.toString()
        );

        return this._http.get<TransactionTimeResponse>(url);
    }
}
