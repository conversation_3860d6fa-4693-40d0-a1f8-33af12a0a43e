/**
 * @fileoverview Data layer service for handling transaction details API calls
 * @description This service extends the base API service to handle transaction details
 *              operations. It provides methods to fetch transaction information using
 *              GUID parameters and manages the API communication for transaction data.
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-01-27
 */

import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiServiceBase } from './api-service-base-dl.service';
import { GuidParams } from '../../interface/reservation.interface';
import { TransactionResponse } from '../../interface/transaction.interface';

/**
 * TransactionDetailsDL - Data Layer Service for Transaction Details
 *
 * This service handles all API operations related to transaction details.
 * It extends the base API service and provides specific functionality for:
 * - Fetching transaction details by GUID
 * - Building API parameters for transaction requests
 * - Managing transaction data communication
 *
 * The service follows the data layer pattern and is responsible for:
 * - API endpoint configuration
 * - Parameter building and validation
 * - HTTP request handling
 * - Response type safety
 */
@Injectable({ providedIn: 'root' })
export class TransactionDetailsDL extends ApiServiceBase {
    /**
     * API parameters for transaction details request
     * Stores the GUID and other parameters needed for the API call
     */
    private _apiParams!: GuidParams;

    /**
     * Constructor - Initializes the service with endpoint configuration
     *
     * Sets up the service with the 'transaction_details' endpoint and
     * 'GET_TRANSACTION_DETAILS' operation identifier.
     */
    constructor() {
        super('transaction_details', 'GET_TRANSACTION_DETAILS');
        this.init();
    }

    /**
     * Builds API parameters for the transaction details request
     *
     * This method is called by the business layer to set the parameters
     * before making the API call. It stores the GUID and other required
     * parameters for the transaction details request.
     *
     * @param data - GuidParams object containing the transaction GUID
     */
    buildApiParams(data: GuidParams) {
        this._apiParams = data;
    }

    /**
     * Loads transaction details from the API
     *
     * This method constructs the API URL with the provided GUID and
     * makes an HTTP GET request to fetch transaction details.
     * The response includes complete transaction information including
     * line items, payment details, and customer information.
     *
     * @returns Observable<TransactionResponse> - Transaction details response
     */
    load(): Observable<TransactionResponse> {
        const { guid } = this._apiParams;
        const url = this.getApiUrl().replace('{transactionGuid}', guid);

        return this._http.get<TransactionResponse>(url);
    }
}
