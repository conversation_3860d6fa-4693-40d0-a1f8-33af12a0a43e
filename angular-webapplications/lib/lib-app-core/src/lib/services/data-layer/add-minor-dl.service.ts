/**
 * @fileoverview Add Minor Data Layer Service
 * <AUTHOR>
 * @created 2025-08-14
 * @version 1.0.0
 */
import { Injectable } from '@angular/core';
import { ApiServiceBase, appCoreConstant } from 'lib-app-core';
import { AddMinorResponseDTOModel } from '../../models/add-minor-response.dto';
import { Observable } from 'rxjs';

type AddMinorParams = {
    customerId: string;
};

@Injectable()
export class AddMinorServiceDL extends ApiServiceBase {
    private _apiParams: AddMinorParams = {
        customerId: '',
    };  

    private _apiPayload: any = {};

    buildApiParams(params: AddMinorParams) {
        this._apiParams = params;
    }

    buildApiPayload(payload: any) {
        this._apiPayload = payload;
    }

    constructor() {
        super(
            appCoreConstant.TRANSFER_STATE_KEYS.ADD_MINOR_DATA,
            'ADD_MINOR'
        );
        this.init();
    }

    load(): Observable<AddMinorResponseDTOModel> {
        const url = this.getApiUrl(this._apiParams);
        return this._http.post<AddMinorResponseDTOModel>(url, this._apiPayload);
    }
}
