/**
 * @fileoverview Language Container Business Service (Uses Shared Language Signal)
 * <AUTHOR>
 * @created 2025-08-21
 * @version 1.0.0
 */

import { computed, inject, Injectable, signal } from '@angular/core';
import { LanguageContainerDTOModel } from '../../models/language-container-dto.model';
import { AppInitBaseService } from '../app-init-base.service';
import { LanguageContainerServiceDL } from '../data-layer/language-container-dl.service';

@Injectable({ providedIn: 'root' })
export class LanguageContainerServiceBL {
    private readonly _languageContainerServiceDL = inject(
        LanguageContainerServiceDL
    );
    private readonly _appInitBaseService = inject(AppInitBaseService);
    readonly languageContainer = signal<LanguageContainerDTOModel[]>([]);

    readonly selectedLanguage = computed(() =>
        this.languageContainer().find(
            (lang) => lang.LanguageCode === this._appInitBaseService.languageId$
        )
    );

    initializeLanguageContainer() {
        this._languageContainerServiceDL.subscribeToData(
            (languages: LanguageContainerDTOModel[]) => {
                this.languageContainer.set(
                    languages.sort((a, b) =>
                        a.LanguageCode.localeCompare(b.LanguageCode)
                    )
                );
            }
        );
    }

    selectLanguage(language: LanguageContainerDTOModel) {
        this._appInitBaseService.updateLanguageId(language.LanguageCode);
    }
}
