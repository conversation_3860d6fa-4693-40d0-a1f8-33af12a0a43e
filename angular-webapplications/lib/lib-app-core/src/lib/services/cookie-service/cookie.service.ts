/**
 * @fileoverview Cookie service for setting and getting cookies
 * <AUTHOR>
 * @created 2025-08-26
 * @version 1.0.0
 */
import { isPlatformBrowser, isPlatformServer } from '@angular/common';
import { inject, Injectable, PLATFORM_ID } from '@angular/core';
import { Request, Response } from 'express';
import { REQUEST, RESPONSE } from '../../utils/injection-tokens';
import { EnvService } from 'lib-app-core';
import { appCoreConstant } from '../../constants/app-core.constant';
export interface CookieOptions {
    expires?: Date | number;
    path?: string;
    domain?: string;
    secure?: boolean;
    sameSite?: 'lax' | 'strict' | 'none' | boolean;
    httpOnly?: boolean;
    maxAge?: number; // seco
}

@Injectable({ providedIn: 'root' })
export class CookieService {
    private readonly platformId = inject(PLATFORM_ID);
    private readonly request = inject<Request>(REQUEST, { optional: true });
    private readonly response = inject<Response>(RESPONSE, { optional: true });
    private readonly envService = inject(EnvService);

    // Regex constants for parsing domain names
    private readonly STRIP_SCHEME_REGEX = /^[a-z]+:\/\//i;
    private readonly STRIP_IPV6_BRACKETS_REGEX = /^\[|]$/g;
    private readonly STRIP_PORT_REGEX = /:\d+$/;

    /**
     * @description Check if the current environment is a server.
     * @returns {boolean} True if the current environment is a server, false otherwise.
     */
    private isServer(): boolean {
        return isPlatformServer(this.platformId);
    }

    /**
     * @description Check if the current environment is a browser.
     * @returns {boolean} True if the current environment is a browser, false otherwise.
     */
    private isBrowser(): boolean {
        return isPlatformBrowser(this.platformId);
    }

    /**
     * Parses the application's domain name to a format suitable for the `Domain` cookie attribute.
     * It handles various formats (full URLs, IPs, localhost) to return a valid domain or an empty string
     * for host-only cookies (like on localhost or IP addresses).
     * @returns The processed cookie domain string, or an empty string.
     */
    private getCookieDomain(): string {
        const raw = (this.envService.getDomainName() || '').trim();
        if (!raw) return '';

        // Strip scheme, path, port, and IPv6 brackets to isolate the host.
        let host = raw.replace(this.STRIP_SCHEME_REGEX, '').split('/')[0];
        host = host.replace(this.STRIP_IPV6_BRACKETS_REGEX, ''); // [::1] -> ::1
        host = host.replace(this.STRIP_PORT_REGEX, ''); // :4200 -> ''

        // Check if the host is an IP address.
        const isIPv4 = appCoreConstant.IP_ADDRESS_REGEX.test(host);
        const isIPv6 = host.includes(':');

        // For localhost, .local domains, and IP addresses, a domain attribute should not be set.
        // This results in a "host-only" cookie.
        if (
            host === 'localhost' ||
            host.endsWith('.local') ||
            isIPv4 ||
            isIPv6
        ) {
            return '';
        }

        // Return the host, removing any leading dot as it's an outdated practice
        return host.startsWith('.') ? host.slice(1) : host;
    }

    /**
     * Determines if the current connection is secure (HTTPS).
     * On the server, it checks request headers; in the browser, it checks `window.location.protocol`.
     * @returns True if the connection is HTTPS, false otherwise.
     */
    private isHttps(): boolean {
        return this.isServer()
            ? !!(
                  this.request?.secure ||
                  String(this.request?.headers['x-forwarded-proto'] || '')
                      .split(',')[0]
                      .trim()
                      .toLowerCase() === 'https'
              )
            : this.isBrowser()
            ? window.location.protocol === 'https:'
            : false;
    }

    /**
     * Sets a cookie with the given name, value, and options.
     * Automatically handles platform-specific logic for setting cookies.
     * @param name The name of the cookie.
     * @param value The value of the cookie.
     * @param options Optional configuration for the cookie.
     */
    setCookie(name: string, value: string, options: CookieOptions = {}): void {
        const domain = this.getCookieDomain();

        // Merge default options with user-provided options.
        const merged: CookieOptions = {
            path: '/',
            // Use Secure only on HTTPS
            secure: this.isHttps(),
            sameSite: 'lax',
            httpOnly: false,
            ...options,
        };

        // if SameSite is 'None', the 'Secure' attribute MUST be set.
        if (merged.sameSite === 'none') merged.secure = true;

        // Only set the domain attribute if it's a valid domain (not IP/localhost)
        // and the user hasn't provided a specific domain.
        if (!domain) {
            delete merged.domain;
        } else if (!options.domain) {
            // Only attach if caller didn’t override
            merged.domain = domain;
        }

        const cookieString = this.buildCookieString(name, value, merged);

        if (this.isServer()) {
            if (!this.response || !this.request) return;
            // Append the new cookie to the 'Set-Cookie' header, preserving existing ones.
            const existing = this.response.getHeader('Set-Cookie');
            const cookieHeader = Array.isArray(existing)
                ? [...existing, cookieString]
                : existing
                ? [existing as string, cookieString]
                : [cookieString];
            this.response.setHeader('Set-Cookie', cookieHeader);
        } else if (this.isBrowser()) {
            document.cookie = cookieString;
        }
    }

    /**
     * Constructs a valid `Set-Cookie` header string from a name, value, and options.
     * @param name The name of the cookie.
     * @param value The value of the cookie.
     * @param options The cookie options.
     * @returns A formatted cookie string.
     */
    private buildCookieString(
        name: string,
        value: string,
        options: CookieOptions
    ): string {
        const parts = [`${name}=${encodeURIComponent(value)}`];

        if (options.expires) {
            const expiresDate =
                options.expires instanceof Date
                    ? options.expires
                    : new Date(options.expires);
            parts.push(`Expires=${expiresDate.toUTCString()}`);
        }
        if (typeof options.maxAge === 'number') {
            parts.push(`Max-Age=${Math.max(0, Math.floor(options.maxAge))}`);
        }
        if (options.path) parts.push(`Path=${options.path}`);
        if (options.domain && options.domain.trim() !== '')
            parts.push(`Domain=${options.domain}`);

        if (options.secure) parts.push(`Secure`);

        // Normalize SameSite casing: lax|strict|none -> Lax|Strict|None
        if (options.sameSite !== undefined) {
            const s = (
                typeof options.sameSite === 'string'
                    ? options.sameSite
                    : options.sameSite
                    ? 'Lax'
                    : 'Lax'
            )
                .toString()
                .toLowerCase();
            const normalized =
                s === 'none' ? 'None' : s === 'strict' ? 'Strict' : 'Lax';
            parts.push(`SameSite=${normalized}`);
        }

        // HttpOnly can only be set server-side
        if (options.httpOnly && this.isServer()) parts.push(`HttpOnly`);

        return parts.join('; ');
    }

    /**
     * Retrieves the value of a single cookie by its name.
     * @param name The name of the cookie to retrieve.
     * @returns The cookie's value, or null if not found.
     */
    getCookie(name: string): string | null {
        if (this.isServer()) {
            if (!this.request) return null;
            const cookieHeader = this.request.headers?.cookie || '';
            const cookies = this.parseCookies(cookieHeader);
            return cookies[name] || null;
        } else if (this.isBrowser()) {
            const nameEQ = name + '=';
            const cookies = document.cookie.split(';');
            for (let cookie of cookies) {
                cookie = cookie.trim();
                if (cookie.startsWith(nameEQ)) {
                    return decodeURIComponent(cookie.substring(nameEQ.length));
                }
            }
        }
        return null;
    }

    /**
     * Deletes a cookie by name.
     * On the server, it uses `response.clearCookie`. In the browser, it sets the cookie's
     * expiration date to the past.
     * @param name The name of the cookie to delete.
     * @param options The path and domain options of the cookie to delete. Must match the original.
     */
    deleteCookie(
        name: string,
        options: CookieOptions = {
            path: '/',
            secure: false,
            sameSite: 'lax',
            httpOnly: false,
        }
    ): void {
        if (this.isServer()) {
            if (this.response && this.request) {
                this.response.clearCookie(name, {
                    path: options.path,
                    domain: options.domain, // leave undefined for IP/localhost
                    secure: options.secure,
                    sameSite: options.sameSite,
                    httpOnly: options.httpOnly,
                });
            }
        } else if (this.isBrowser()) {
            // To delete a cookie, set its value to empty and expiration to a past date.
            const expired = new Date(0);
            this.setCookie(name, '', { ...options, expires: expired });
        }
    }

    /**
     * Checks if a cookie with the specified name exists.
     * @param name The name of the cookie to check.
     * @returns True if the cookie exists, false otherwise.
     */
    hasCookie(name: string): boolean {
        return this.getCookie(name) !== null;
    }

    /**
     * Retrieves all cookies as a key-value object.
     * @returns A record of all cookies.
     */
    getAllCookies(): Record<string, string> {
        if (this.isServer()) {
            if (!this.request) return {};
            const cookieHeader = this.request.headers?.cookie || '';
            return this.parseCookies(cookieHeader);
        } else {
            const cookies: Record<string, string> = {};
            const cookieArray = document.cookie.split(';');
            for (let cookie of cookieArray) {
                const [rawName, ...rawValue] = cookie.trim().split('=');
                if (rawName && rawValue) {
                    cookies[decodeURIComponent(rawName)] = decodeURIComponent(
                        rawValue.join('=')
                    );
                }
            }
            return cookies;
        }
    }

    /**
     * Retrieves a cookie and parses its value as JSON.
     * @param name The name of the cookie.
     * @returns The parsed object, or null if the cookie doesn't exist or parsing fails.
     */
    getCookieAsObject<T = any>(name: string): T | null {
        const value = this.getCookie(name);
        try {
            return value ? JSON.parse(value) : null;
        } catch {
            return null;
        }
    }

    /**
     * Converts an object to a JSON string and stores it in a cookie.
     * @param name The name of the cookie.
     * @param value The object to store.
     * @param options Optional configuration for the cookie.
     */
    setCookieAsObject<T>(
        name: string,
        value: T,
        options: CookieOptions = {}
    ): void {
        this.setCookie(name, JSON.stringify(value), options);
    }

    /**
     * Parses a raw cookie header string (e.g., "key1=val1; key2=val2") into an object.
     * @param cookieHeader The raw cookie string from the request header.
     * @returns A key-value record of the cookies.
     */
    private parseCookies(cookieHeader: string): Record<string, string> {
        return cookieHeader.split(';').reduce((acc, part) => {
            const [key, ...val] = part.trim().split('=');
            if (key && val) {
                acc[key] = decodeURIComponent(val.join('='));
            }
            return acc;
        }, {} as Record<string, string>);
    }
}
