import { TestBed } from '@angular/core/testing';
import { PLATFORM_ID } from '@angular/core';
import { CookieService } from './cookie.service';
import { REQUEST, RESPONSE } from '../../utils/injection-tokens';
import { Request, Response } from 'express';

// Mock Express Request and Response
const createMockRequest = (cookieHeader?: string): Partial<Request> => ({
    headers: {
        cookie: cookieHeader || ''
    }
});

const createMockResponse = (): Partial<Response> => {
    const headers: Record<string, string | string[]> = {};
    return {
        setHeader: (name: string, value: string | string[]) => {
            headers[name] = value; 
            return {} as Response;
        },
        getHeader: (name: string) => headers[name],
        clearCookie: (name: string) => {
            // Mock clearCookie implementation
            const existing = headers['Set-Cookie'] as string[] || [];
            headers['Set-Cookie'] = existing.filter(cookie => !cookie.startsWith(`${name}=`));
            return {} as Response;
        }
    };
};

const clearBrowserCookies = () => {
    if (typeof document !== 'undefined') {
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const cookieParts = cookie.split('=');
            const hasEqualsSign = cookieParts.length > 1;

            const cookieName = hasEqualsSign
                ? cookieParts[0].trim()
                : cookie.trim();

            // Delete the cookie by setting it to an expired date
            document.cookie = `${encodeURIComponent(
                cookieName
            )}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/`;
        }
    }
};

describe('CookieService - Browser Environment', () => {
    let cookieService: CookieService;

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [
                CookieService,
                { provide: PLATFORM_ID, useValue: 'browser' },
            ],
        });
        cookieService = TestBed.inject(CookieService);
    });

    afterEach(() => {
        clearBrowserCookies();
    });

    it('should be created', () => {
        expect(cookieService).toBeTruthy();
    });

    it('should set and get a cookie', () => {
        cookieService.setCookie('testName', 'testValue');
        expect(cookieService.getCookie('testName')).toBe('testValue');
    });

    it('should return null for non-existent cookie', () => {
        expect(cookieService.getCookie('nonExistent')).toBeNull();
    });

    it('should delete a cookie', () => {
        cookieService.setCookie('toDelete', 'value');
        cookieService.deleteCookie('toDelete');
        expect(cookieService.getCookie('toDelete')).toBeNull();
    });

    it('should confirm cookie existence', () => {
        cookieService.setCookie('exists', 'yes');
        expect(cookieService.hasCookie('exists')).toBeTrue();
        expect(cookieService.hasCookie('missing')).toBeFalse();
    });

    it('should store and retrieve a cookie as object', () => {
        const obj = { a: 1, b: 'two' };
        cookieService.setCookieAsObject('jsonCookie', obj);
        expect(cookieService.getCookieAsObject('jsonCookie')).toEqual(obj);
    });

    it('should set cookie with object and options', () => {
        const obj = { x: 123 };
        cookieService.setCookieAsObject('objectWithOptions', obj, {
            path: '/',
            sameSite: 'lax',
        });
        expect(cookieService.getCookieAsObject('objectWithOptions')).toEqual(obj);
    });

    it('should return null for invalid JSON in getCookieAsObject', () => {
        cookieService.setCookie('badJson', '%7Bbad'); // corrupted value
        expect(cookieService.getCookieAsObject('badJson')).toBeNull();
    });

    it('should get all cookies as object', () => {
        cookieService.setCookie('cookie1', 'val1');
        cookieService.setCookie('cookie2', 'val2');
        const all = cookieService.getAllCookies();
        expect(all['cookie1']).toBe('val1');
        expect(all['cookie2']).toBe('val2');
    });

    it('should throw error if SameSite=None is used without Secure', () => {
        expect(() => {
            cookieService.setCookie('crossSite', 'token', {
                sameSite: 'none',
                secure: false, // insecure on purpose
            });
        }).toThrowError(
            "Cookies with SameSite=None must also set 'secure: true' or they will be rejected by modern browsers."
        );
    });

    it('should allow SameSite=None when Secure is true', () => {
        expect(() => {
            cookieService.setCookie('safeCrossSite', 'token', {
                sameSite: 'none',
                secure: true,
            });
        }).not.toThrow();
        expect(cookieService.getCookie('safeCrossSite')).toBe('token');
    });

    it('should handle URL encoding/decoding correctly', () => {
        const specialValue = 'test=value; with spaces & symbols!';
        cookieService.setCookie('special', specialValue);
        expect(cookieService.getCookie('special')).toBe(specialValue);
    });

    it('should set cookie with all options', () => {
        const expires = new Date('2025-12-31');
        cookieService.setCookie('fullOptions', 'value', {
            expires,
            path: '/',
            sameSite: 'none',
            secure: true,
            domain: 'localhost',
            httpOnly: true
        });
        expect(cookieService.getCookie('fullOptions')).toBe('value');
    });
});

describe('CookieService - Server Environment', () => {
    let cookieService: CookieService;
    let mockRequest: Partial<Request>;
    let mockResponse: Partial<Response>;

    beforeEach(() => {
        mockRequest = createMockRequest();
        mockResponse = createMockResponse();

        TestBed.configureTestingModule({
            providers: [
                CookieService,
                { provide: PLATFORM_ID, useValue: 'server' },
                { provide: REQUEST, useValue: mockRequest },
                { provide: RESPONSE, useValue: mockResponse },
            ],
        });
        cookieService = TestBed.inject(CookieService);
    });

    it('should be created in server environment', () => {
        expect(cookieService).toBeTruthy();
    });

    it('should set cookie on server', () => {
        cookieService.setCookie('serverCookie', 'serverValue');
        
        const setCookieHeader = mockResponse.getHeader?.('Set-Cookie') as string[];
        expect(setCookieHeader).toBeDefined();
        expect(setCookieHeader[0]).toContain('serverCookie=serverValue');
    });

    it('should get cookie from request headers', () => {
        mockRequest = createMockRequest('testCookie=testValue; otherCookie=otherValue');
        
        TestBed.resetTestingModule();
        TestBed.configureTestingModule({
            providers: [
                CookieService,
                { provide: PLATFORM_ID, useValue: 'server' },
                { provide: REQUEST, useValue: mockRequest },
                { provide: RESPONSE, useValue: mockResponse },
            ],
        });
        cookieService = TestBed.inject(CookieService);

        expect(cookieService.getCookie('testCookie')).toBe('testValue');
        expect(cookieService.getCookie('otherCookie')).toBe('otherValue');
    });

    it('should return null for non-existent cookie on server', () => {
        expect(cookieService.getCookie('nonExistent')).toBeNull();
    });

    it('should delete cookie on server', () => {
        cookieService.setCookie('toDelete', 'value');
        cookieService.deleteCookie('toDelete');
        
        // Verify clearCookie was called (mocked implementation)
        const setCookieHeader = mockResponse.getHeader?.('Set-Cookie') as string[];
        expect(setCookieHeader).toBeDefined();
    });

    it('should confirm cookie existence on server', () => {
        mockRequest = createMockRequest('exists=yes');
        
        TestBed.resetTestingModule();
        TestBed.configureTestingModule({
            providers: [
                CookieService,
                { provide: PLATFORM_ID, useValue: 'server' },
                { provide: REQUEST, useValue: mockRequest },
                { provide: RESPONSE, useValue: mockResponse },
            ],
        });
        cookieService = TestBed.inject(CookieService);

        expect(cookieService.hasCookie('exists')).toBeTrue();
        expect(cookieService.hasCookie('missing')).toBeFalse();
    });

    it('should store and retrieve object cookie on server', () => {
        const obj = { server: 'data', number: 42 };
        cookieService.setCookieAsObject('serverJson', obj);
        
        const setCookieHeader = mockResponse.getHeader?.('Set-Cookie') as string[];
        expect(setCookieHeader[0]).toContain('serverJson=');
        expect(setCookieHeader[0]).toContain(encodeURIComponent(JSON.stringify(obj)));
    });

    it('should get all cookies from request headers', () => {
        mockRequest = createMockRequest('cookie1=val1; cookie2=val2; cookie3=val3');
        
        TestBed.resetTestingModule();
        TestBed.configureTestingModule({
            providers: [
                CookieService,
                { provide: PLATFORM_ID, useValue: 'server' },
                { provide: REQUEST, useValue: mockRequest },
                { provide: RESPONSE, useValue: mockResponse },
            ],
        });
        cookieService = TestBed.inject(CookieService);

        const all = cookieService.getAllCookies();
        expect(all['cookie1']).toBe('val1');
        expect(all['cookie2']).toBe('val2');
        expect(all['cookie3']).toBe('val3');
    });

    it('should handle empty cookie header', () => {
        mockRequest = createMockRequest('');
        
        TestBed.resetTestingModule();
        TestBed.configureTestingModule({
            providers: [
                CookieService,
                { provide: PLATFORM_ID, useValue: 'server' },
                { provide: REQUEST, useValue: mockRequest },
                { provide: RESPONSE, useValue: mockResponse },
            ],
        });
        cookieService = TestBed.inject(CookieService);

        expect(cookieService.getAllCookies()).toEqual({});
        expect(cookieService.getCookie('any')).toBeNull();
    });

    it('should set cookie with all server options', () => {
        const expires = new Date('2025-12-31');
        cookieService.setCookie('serverFull', 'value', {
            expires,
            path: '/api',
            domain: 'example.com',
            secure: true,
            sameSite: 'strict',
            httpOnly: true
        });

        const setCookieHeader = mockResponse.getHeader?.('Set-Cookie') as string[];
        expect(setCookieHeader[0]).toContain('serverFull=value');
        expect(setCookieHeader[0]).toContain('Expires=');
        expect(setCookieHeader[0]).toContain('Path=/api');
        expect(setCookieHeader[0]).toContain('Domain=example.com');
        expect(setCookieHeader[0]).toContain('Secure');
        expect(setCookieHeader[0]).toContain('SameSite=Strict');
        expect(setCookieHeader[0]).toContain('HttpOnly');
    });

    it('should handle multiple cookies in Set-Cookie header', () => {
        cookieService.setCookie('first', 'value1');
        cookieService.setCookie('second', 'value2');
        cookieService.setCookie('third', 'value3');

        const setCookieHeader = mockResponse.getHeader?.('Set-Cookie') as string[];
        expect(setCookieHeader.length).toBe(3);
        expect(setCookieHeader[0]).toContain('first=value1');
        expect(setCookieHeader[1]).toContain('second=value2');
        expect(setCookieHeader[2]).toContain('third=value3');
    });

    it('should handle URL encoding/decoding on server', () => {
        const specialValue = 'test=value; with spaces & symbols!';
        cookieService.setCookie('serverSpecial', specialValue);
        
        const setCookieHeader = mockResponse.getHeader?.('Set-Cookie') as string[];
        expect(setCookieHeader[0]).toContain(encodeURIComponent(specialValue));
    });

    it('should return null when request is not available', () => {
        TestBed.resetTestingModule();
        TestBed.configureTestingModule({
            providers: [
                CookieService,
                { provide: PLATFORM_ID, useValue: 'server' },
                // No REQUEST provider
                { provide: RESPONSE, useValue: mockResponse },
            ],
        });
        cookieService = TestBed.inject(CookieService);

        expect(cookieService.getCookie('any')).toBeNull();
        expect(cookieService.getAllCookies()).toEqual({});
    });

    it('should handle missing response gracefully', () => {
        TestBed.resetTestingModule();
        TestBed.configureTestingModule({
            providers: [
                CookieService,
                { provide: PLATFORM_ID, useValue: 'server' },
                { provide: REQUEST, useValue: mockRequest },
                // No RESPONSE provider
            ],
        });
        cookieService = TestBed.inject(CookieService);

        // Should not throw
        expect(() => {
            cookieService.setCookie('test', 'value');
        }).not.toThrow();
    });
});

describe('CookieService - Platform Detection', () => {
    it('should handle unknown platform gracefully', () => {
        TestBed.configureTestingModule({
            providers: [
                CookieService,
                { provide: PLATFORM_ID, useValue: 'unknown' },
            ],
        });
        const cookieService = TestBed.inject(CookieService);

        // Should not throw and should return null for get operations
        expect(() => {
            cookieService.setCookie('test', 'value');
        }).not.toThrow();

        expect(cookieService.getCookie('test')).toBeNull();
        expect(cookieService.getAllCookies()).toEqual({});
    });
});