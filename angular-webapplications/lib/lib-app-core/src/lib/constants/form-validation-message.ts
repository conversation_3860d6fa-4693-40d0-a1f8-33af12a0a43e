/**
 * @fileoverview Form validation messages for email, password, and password reset functionality
 * <AUTHOR>
 * @version 1.0.0
 */

export const formValidationMessages = {
    email: {
        required: $localize`:form-validation-message.email.required@@form-validation-message.email.required:Email address is required`,
        invalidEmail: $localize`:form-validation-message.email.invalidEmail@@form-validation-message.email.invalidEmail:Please enter a valid email address`,
    },
    password: {
        required: $localize`:form-validation-message.password.required@@form-validation-message.password.required:Password is required`,
        pattern: $localize`:form-validation-message.password.pattern@@form-validation-message.password.pattern:At least 8 characters, 1 uppercase letter, 1 lowercase letter, 1 number and 1 special character`,
    },
    confirmPassword: {
        required: $localize`:form-validation-message.confirmPassword.required@@form-validation-message.confirmPassword.required:Please confirm your password`,
        passwordMismatch: $localize`:form-validation-message.confirmPassword.passwordMismatch@@form-validation-message.confirmPassword.passwordMismatch:Passwords do not match`,
    },
    dateOfBirth: {
        belowMinorAge: $localize`:form-validation-message.dateOfBirth.belowMinorAge@@form-validation-message.dateOfBirth.belowMinorAge:Primary account holder must be above {age} years`,
        aboveMinorAge: $localize`:form-validation-message.dateOfBirth.aboveMinorAge@@form-validation-message.dateOfBirth.aboveMinorAge:Minor must be below {age} years`,
        futureDate: $localize`:form-validation-message.dateOfBirth.futureDate@@form-validation-message.dateOfBirth.futureDate:Date of birth cannot be in the future`,
    },
} as const;

export const invalidFormat = $localize`:form-validation-message.invalidFormat@@form-validation-message.invalidFormat:Invalid format`;

// Password Reset Messages
export const passwordResetMessages = {
    success: $localize`:form-validation-message.passwordReset.success@@form-validation-message.passwordReset.success:Your Password has been successfully reset.`,
    failure: $localize`:form-validation-message.passwordReset.failure@@form-validation-message.passwordReset.failure:Password reset failed. Please try again.`,
    invalidToken: $localize`:form-validation-message.passwordReset.invalidToken@@form-validation-message.passwordReset.invalidToken:Invalid reset token`,
    invalidParameters: $localize`:form-validation-message.passwordReset.invalidParameters@@form-validation-message.passwordReset.invalidParameters:Invalid parameters`,
    noCardData: $localize`:form-validation-message.passwordReset.noCardData@@form-validation-message.passwordReset.noCardData:No card data received`,
} as const;

// Waiver Error Messages
export const waiverErrorMessages = {
    success: $localize`:form-validation-message.waiverErrorMessages.success@@form-validation-message.waiverErrorMessages.success:Your Password has been successfully reset.`,
    failure: $localize`:form-validation-message.waiverErrorMessages.failure@@form-validation-message.waiverErrorMessages.failure:Password reset failed. Please try again.`,
    invalidToken: $localize`:form-validation-message.waiverErrorMessages.invalidToken@@form-validation-message.waiverErrorMessages.invalidToken:Invalid reset token`,
    invalidParameters: $localize`:form-validation-message.waiverErrorMessages.invalidParameters@@form-validation-message.waiverErrorMessages.invalidParameters:Invalid parameters`,
    noCardData: $localize`:form-validation-message.waiverErrorMessages.noCardData@@form-validation-message.waiverErrorMessages.noCardData:Guid data not received`,
    noReservationCode: $localize`:form-validation-message.waiverErrorMessages.noReservationCode@@form-validation-message.waiverErrorMessages.noReservationCode:No reservation code received`,
    noGuidId: $localize`:form-validation-message.waiverErrorMessages.noGuidId@@form-validation-message.waiverErrorMessages.noGuidId:No guid id received`,
} as const;
