/**
 * @fileoverview Transaction-related interfaces for the core application
 * @description This file contains TypeScript interfaces that define the data structures
 *              for transaction time operations and API responses used across the application.
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-08-15
 */

/**
 * TransactionTimeParams - Parameters for time-based transaction queries
 *
 * This interface defines parameters used when querying transactions
 * based on time or booking information.
 */
export interface TransactionTimeParams {
    transactionId: number;
}

/**
 * TransactionTimeDTO - Represents booking and time-based transaction data
 *
 * This interface defines the structure for booking information, including
 * reservation details, customer information, facility data, and scheduling
 * information. Used for time-based activities and reservations.
 */
export interface TransactionTimeDTO {
    BookingId: number;
    BookingClassId: number;
    BookingName: string;
    FromDate: string;
    RecurFlag: string;
    RecurFrequency: string;
    RecurEndDate: string | null;
    Quantity: number;
    ReservationCode: string;
    Status: string;
    CardId: number;
    CardNumber: string;
    CustomerId: number;
    CustomerName: string;
    ExpiryTime: string | null;
    Channel: string;
    Remarks: string;
    ContactNo: string;
    AlternateContactNo: string;
    Email: string;
    IsEmailSent: number;
    ToDate: string;
    TrxId: number;
    TrxNumber: string;
    TrxStatus: string;
    TrxNetAmount: number;
    Age: number;
    Gender: string;
    PostalAddress: string;
    BookingProductId: number;
    BookingProductName: string;
    FacilityName: string;
    FacilityMapId: number;
    FacilityMapName: string;
    AttractionScheduleId: number;
    ExtraGuests: number;
    CreatedBy: string;
    CreationDate: string;
    LastUpdatedBy: string;
    LastupdateDate: string;
    Guid: string;
    SiteId: number;
    SynchStatus: boolean;
    MasterEntityId: number;
    BookingCheckListDTOList: any[] | null;
    ServiceChargeAmount: number;
    ServiceChargePercentage: number;
    IsChanged: boolean;
}

/**
 * TransactionTimeResponse - API response wrapper for time-based transaction data
 *
 * This interface defines the structure of API responses that return
 * time-based transaction or booking data.
 */
export interface TransactionTimeResponse {
    data: TransactionTimeDTO[];
}

/**
 * TransactionDTO - Represents a complete transaction
 *
 * This interface defines the main transaction structure containing
 * header information, payment details, customer data, and associated
 * line items. This is the primary data structure for financial transactions.
 */
export interface TransactionDTO {
    TransactionId: number;
    TransactionDate: string;
    TransactionAmount: number;
    TransactionDiscountPercentage: number;
    TransactionDiscountAmount: number;
    TaxAmount: number;
    TransactionNetAmount: number;
    PosMachine: string;
    UserId: number;
    PaymentMode: number;
    PaymentModeName: string | null;
    CashAmount: number;
    CreditCardAmount: number;
    GameCardAmount: number;
    PaymentReference: string;
    PrimaryCardId: number;
    OrderId: number;
    POSTypeId: number;
    TransactionNumber: string;
    TransactionOTP: string;
    Remarks: string;
    POSMachineId: number;
    OtherPaymentModeAmount: number;
    Status: string;
    TransactionProfileId: number;
    LastUpdateTime: string;
    LastUpdatedBy: string;
    TokenNumber: string;
    OriginalSystemReference: string;
    CustomerId: number;
    ExternalSystemReference: string;
    ReprintCount: number;
    OriginalTransactionId: number;
    OrderTypeGroupId: number;
    TableNumber: string;
    Paid: number;
    UserName: string;
    CreatedBy: number;
    CreationDate: string;
    Guid: string;
    SynchStatus: boolean;
    SiteId: number;
    MasterEntityId: number;
    Selected: boolean;
    Tickets: any | null;
    Receipt: any | null;
    TicketsHTML: any | null;
    ReceiptHTML: any | null;
    VisitDate: string | null;
    ApplyVisitDate: boolean;
    IsChanged: boolean;
    IsChangedRecursive: boolean;
    TransactionLinesDTOList: any[];
    TrxPaymentsDTOList: any[];
    DiscountsSummaryDTOList: any | null;
    DiscountApplicationHistoryDTOList: any | null;
    PrimaryCard: string;
    ReceiptDTO: any | null;
    TicketDTOList: any | null;
    TicketPrinterMapDTOList: any | null;
    CustomerName: string;
    CommitTransaction: boolean;
    SaveTransaction: boolean;
    CloseTransaction: boolean;
    ApplyOffset: boolean;
    PaymentProcessingCompleted: boolean;
    ReverseTransaction: boolean;
    CustomerIdentifier: string;
    GuestName: string;
    TrxPOSPrinterOverrideRulesDTO: any | null;
    TransctionOrderDispensingDTO: any | null;
}

/**
 * TransactionResponse - API response wrapper for transaction data
 *
 * This interface defines the structure of API responses that return
 * transaction data, including pagination information and authentication tokens.
 */
export interface TransactionResponse {
    data: TransactionDTO[];
    currentPageNo: number;
    totalCount: number;
    token: string;
}
