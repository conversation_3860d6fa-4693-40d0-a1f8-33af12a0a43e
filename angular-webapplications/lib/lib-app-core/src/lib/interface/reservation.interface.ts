/**
 * @fileoverview Reservation-related interfaces for the core application
 * @description This file contains TypeScript interfaces that define the data structures
 *              for reservation operations, party card states, and query parameters
 *              used across the application.
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-08-15
 */

/**
 * GuidParams - Parameters for GUID-based API operations
 *
 * This interface defines parameters used when making API calls that require
 * a GUID identifier for data retrieval or processing.
 */
export interface GuidParams {
    guid: string;
}

/**
 * IPartyReservationQueryParams - Query parameters for party reservation operations
 *
 * This interface defines the query parameters used when accessing party
 * reservation functionality, typically passed through URL parameters.
 */
export interface IPartyReservationQueryParams {
    reservationCode?: string;
}

/**
 * PartyCardState - State management for party card components
 *
 * This interface defines the state structure for party card components,
 * including loading states, error handling, and data management for
 * waiver sets and party information.
 */
export interface PartyCardState {
    loading: boolean;
    error: string | null;
    waiverSets: any[] | null;
    isSingleParty: boolean;
}
