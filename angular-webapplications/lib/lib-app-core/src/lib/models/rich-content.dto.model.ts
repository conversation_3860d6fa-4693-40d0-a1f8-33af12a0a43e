/**
 * @fileoverview Rich content DTO model
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-08-05
 */
import { BaseDTO } from './base-dto.model';
export class RichContentDTOModel extends BaseDTO<RichContentDTOModel> {
    Id: number = 0;
    ContentName: string = '';
    FileName: string = '';
    Data: string = '';
    IsActive: boolean = true;
    CreatedBy: string = '';
    CreationDate: string = '';
    LastUpdatedBy: string = '';
    LastUpdatedDate: string = '';
    SiteId: number = 0;
    SynchStatus: boolean = false;
    Guid: string = '';
    MasterEntityId: number = -1;
    ContentType: string = '';
    IsChanged: boolean = false;

    constructor() {
        super();
    }
}

/**
 * Container DTO for rich content responses from the API
 */
export class RichContentContainerDTOModel extends BaseDTO<RichContentContainerDTOModel> {
    data: RichContentDTOModel[] = [];

    constructor() {
        super();
    }
}