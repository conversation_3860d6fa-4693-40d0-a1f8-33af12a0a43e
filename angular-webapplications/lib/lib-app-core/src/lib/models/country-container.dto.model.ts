/**
 * @fileoverview Country container DTO model
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-08-05
 */

import { BaseDTO } from 'lib-app-core';

export class StateContainerDTO {
    StateId: number = 0;
    State: string = '';
    Description: string = '';
    CountryId: number = 0;
}

export class CountryContainerDTOModel extends BaseDTO<CountryContainerDTOModel> {
    CountryId: number = 0;
    CountryName: string = '';
    CountryCode: string = '';
    IsActive: boolean = false;
    StateContainerDTOList: StateContainerDTO[] = [];

    constructor() {
        super();
    }
}
