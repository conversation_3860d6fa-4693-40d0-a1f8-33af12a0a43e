/**
 * @fileoverview Add minor response DTO model
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-08-14
 */
import { BaseDTO } from './base-dto.model';

export class AddMinorResponseDTOModel extends BaseDTO<AddMinorResponseDTOModel> {
    data: CustomerRelationshipDTOModel[] = [];

    constructor() {
        super();
    }
}

export class CustomerRelationshipDTOModel extends BaseDTO<CustomerRelationshipDTOModel> {
    Id: number = -1;
    CustomerId: number = -1;
    CustomerName: string | null = null;
    RelatedCustomerId: number = -1;
    RelatedCustomerName: string | null = null;
    CustomerRelationshipTypeId: number = -1;
    EffectiveDate: string | null = null;
    ExpiryDate: string | null = null;
    IsActive: boolean = true;
    CreatedBy: string = '';
    CreationDate: string = '';
    LastUpdatedBy: string = '';
    LastUpdateDate: string = '';
    SiteId: number = -1;
    MasterEntityId: number = -1;
    SynchStatus: boolean = false;
    Guid: string = '';
    CustomerDTO: any = null;
    RelatedCustomerDTO: CreateCustomerResponseDTOModel = new CreateCustomerResponseDTOModel();
    IsChanged: boolean = false;

    constructor() {
        super();
    }
}

export class CreateCustomerResponseDTOModel extends BaseDTO<CreateCustomerResponseDTOModel> {
    Id: number = -1;
    IsActive: boolean = true;
    IsBonusLoaded: boolean = false;
    ProfileId: number = -1;
    MembershipId: number = -1;
    Title: string | null = null;
    FirstName: string | null = null;
    NickName: string | null = null;
    MiddleName: string | null = null;
    LastName: string | null = null;
    ExternalSystemReference: string = '';
    CustomerType: number = 0;
    UniqueIdentifier: string | null = null;
    TaxCode: string | null = null;
    DateOfBirth: string | null = null;
    Gender: string | null = null;
    Anniversary: string | null = null;
    TeamUser: boolean = false;
    RightHanded: boolean = false;
    OptInPromotions: boolean = false;
    OptInPromotionsMode: string | null = null;
    PolicyTermsAccepted: boolean = false;
    Company: string | null = null;
    UserName: string | null = null;
    PhotoURL: string | null = null;
    IdProofFileURL: string | null = null;
    LastLoginTime: string | null = null;
    Designation: string | null = null;
    CustomDataSetId: number = -1;
    Notes: string | null = null;
    CardNumber: string = '';
    Channel: string = '';
    Verified: boolean = false;
    AddressDTOList: any[] | null = null;
    ContactDTOList: any[] | null = null;
    ProfileDTO: ProfileDTOModel = new ProfileDTOModel();
    CustomerVerificationDTO: any = null;
    CustomDataSetDTO: CustomDataSetDTOModel = new CustomDataSetDTOModel();
    PhoneNumber: string = '';
    PhoneContactDTO: any = null;
    Password: string | null = null;
    LatestAddressDTO: AddressDTOModel = new AddressDTOModel();
    SecondaryPhoneNumber: string = '';
    FBUserId: string = '';
    FBAccessToken: string = '';
    TWAccessToken: string = '';
    TWAccessSecret: string = '';
    Email: string = '';
    WeChatAccessToken: string = '';
    IsChanged: boolean = false;
    IsChangedRecursive: boolean = true;
    CustomerCuponsDT: any = null;
    AccountDTOList: any[] = [];
    CustomerMembershipProgressionDTOList: any[] = [];
    CustomerMembershipRewardsLogDTOList: any[] = [];
    CustomerSignedWaiverDTOList: any[] = [];
    CustomerApprovalLogDTOList: any[] = [];
    ActiveCampaignCustomerInfoDTOList: any[] = [];
    CustomerRelationshipDTOList: any[] = [];
    LastVisitedDate: string = '0001-01-01T00:00:00';
    Status: string = '';
    StatusId: number = -1;
    StatusChangeDate: string = '';

    constructor() {
        super();
    }
}

export class ProfileDTOModel extends BaseDTO<ProfileDTOModel> {
    Id: number = -1;
    ProfileTypeId: number = -1;
    ProfileType: number = 1;
    Title: string | null = null;
    FirstName: string | null = null;
    MiddleName: string | null = null;
    LastName: string | null = null;
    NickName: string | null = null;
    Notes: string | null = null;
    DateOfBirth: string | null = null;
    Gender: string | null = null;
    Anniversary: string | null = null;
    PhotoURL: string | null = null;
    RightHanded: boolean = false;
    TeamUser: boolean = false;
    UniqueIdentifier: string | null = null;
    IdProofFileURL: string | null = null;
    TaxCode: string | null = null;
    Designation: string | null = null;
    Company: string | null = null;
    UserName: string | null = null;
    Password: string | null = null;
    LastLoginTime: string | null = null;
    ContactDTOList: any[] | null = null;
    AddressDTOList: any[] | null = null;
    ProfileContentHistoryDTOList: any[] | null = null;
    OptInPromotions: boolean = false;
    OptInPromotionsMode: string | null = null;
    OptInLastUpdatedDate: string | null = null;
    PolicyTermsAccepted: boolean = false;
    IsActive: boolean = true;
    CreatedBy: string | null = null;
    CreationDate: string = '';
    LastUpdatedBy: string | null = null;
    LastUpdateDate: string = '';
    SiteId: number = -1;
    MasterEntityId: number = -1;
    SynchStatus: boolean = false;
    Guid: string | null = null;
    ExternalSystemReference: string | null = null;
    OptOutWhatsApp: boolean = false;
    UserStatus: string | null = null;
    PasswordChangeDate: string | null = null;
    InvalidAccessAttempts: number = 0;
    LockedOutTime: string | null = null;
    PasswordChangeOnNextLogin: boolean = false;
    IsChanged: boolean = true;
    IsChangedRecursive: boolean = true;

    constructor() {
        super();
    }
}

export class CustomDataSetDTOModel extends BaseDTO<CustomDataSetDTOModel> {
    CustomDataSetId: number = -1;
    Dummy: string = '';
    CreatedBy: string | null = null;
    CreationDate: string = '';
    LastUpdatedBy: string | null = null;
    LastUpdateDate: string = '';
    SiteId: number = -1;
    MasterEntityId: number = -1;
    SynchStatus: boolean = false;
    Guid: string | null = null;
    IsChanged: boolean = true;
    IsChangedRecursive: boolean = true;
    CustomDataDTOList: any[] = [];

    constructor() {
        super();
    }
}

export class AddressDTOModel extends BaseDTO<AddressDTOModel> {
    Id: number = -1;
    ProfileId: number = -1;
    AddressTypeId: number = -1;
    AddressType: number = 0;
    Line1: string | null = null;
    Line2: string | null = null;
    Line3: string | null = null;
    City: string | null = null;
    StateId: number = -1;
    CountryId: number = -1;
    PostalCode: string | null = null;
    StateCode: string | null = null;
    StateName: string | null = null;
    CountryName: string | null = null;
    IsActive: boolean = true;
    CreatedBy: string | null = null;
    CreationDate: string = '';
    LastUpdatedBy: string | null = null;
    LastUpdateDate: string = '';
    SiteId: number = -1;
    MasterEntityId: number = -1;
    SynchStatus: boolean = false;
    Guid: string | null = null;
    IsDefault: boolean = false;
    ContactDTOList: any[] = [];
    IsChanged: boolean = true;
    IsChangedRecursive: boolean = true;

    constructor() {
        super();
    }
}