/**
 * @fileoverview
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
import {BaseDTO} from "lib-app-core";

export class CustomerLoginDTOModel extends BaseDTO<CustomerLoginDTOModel> {
    PosMachineGuid: string           = '';
    WebApiToken: string              = '';
    Token: string | null             = null;
    LanguageId: number               = -1;
    IsCorporate: boolean             = false;
    UserPKId: number                 = 0;
    UserRoleId: number               = 0;
    MachineId: number                = 0;
    SiteId: number                   = 0;
    SitePKId: number                 = 0;
    UserId: string                   = '';
    POSMachineName: string           = '';
    LanguageCode: string             = '';
    ExpirySecondsSinceEpoch: number  = 0;
    CustomerGuid: string             = '';

    constructor() {
        super();
    }
}

