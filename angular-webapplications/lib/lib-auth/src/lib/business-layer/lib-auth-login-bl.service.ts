/**
 * @fileoverview Business layer service for handling user login functionality
 * <AUTHOR>
 * @version 1.0.0
 */

import { inject, Injectable, signal } from '@angular/core';
import { LibAuthLoginDL } from '../data-layer/lib-auth-login-dl.service';
import { LibUserDetailDL } from '../data-layer/lib-auth-user-details-dl.service';
import { RequestState } from 'lib/lib-app-core/src/lib/utils/request-state';
import { CookieService } from 'lib/lib-app-core/src/lib/services/cookie-service/cookie.service';
import { CustomerLoginDTOModel } from '../models/customer-login-dto.model';
import { Router } from '@angular/router';
import { WaiverRoutingServiceBL } from 'projects/online-waiver/src/app/services/business-layer/waiver-routing-bl.service';
import { switchMap } from 'rxjs';

@Injectable()
export class LibAuthLoginBL extends RequestState {
    private _libAuthLoginDL = inject(LibAuthLoginDL);
    private _libUserDetailDL = inject(LibUserDetailDL);
    private _cookieService = inject(CookieService);
    private _waiverRoutingService = inject(WaiverRoutingServiceBL);

    loginData = signal<CustomerLoginDTOModel | null>(null);
    
    /**
     * Handles the login process with sequential API calls:
     * 1. USER_LOGIN endpoint to get WebApiToken and CustomerGuid
     * 2. USER_DETAILS endpoint to get correct UserId
     *
     * @param userName - The username for login.
     * @param password - The password for login.
     */
    login(userName: string, password: string) {
        this._libAuthLoginDL.buildApiParams({
            UserName: userName,
            Password: password,
        });
        
        const login$ = this._libAuthLoginDL.load().pipe(
            switchMap((loginResponse) => {
                // Set WebApiToken from first response
                this._cookieService.setCookie('webApiToken', loginResponse.data.WebApiToken);
                
                // Get CustomerGuid for second API call
                const customerGuid = loginResponse.data.CustomerGuid;
                
                // Call USER_DETAILS endpoint
                this._libUserDetailDL.buildApiParams({ guid: customerGuid });
                return this._libUserDetailDL.load();
            })
        );
        
        this._handleRequest(login$, (userDetailsResponse) => {
            // Set UserId from second response
            this._cookieService.setCookie('userId', userDetailsResponse.data[0].Id);
            this._waiverRoutingService.handlePostLoginRouting();
        });
    }
}
