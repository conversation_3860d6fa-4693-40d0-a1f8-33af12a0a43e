/**
 * @fileoverview Data layer service for handling forgot password API calls
 * <AUTHOR>
 * @version 1.0.0
 */

import { inject, Injectable } from '@angular/core';
import { ApiServiceBase, DEFAULT_APP_CONFIG_TOKEN } from 'lib-app-core';
import { Observable, switchMap, tap } from 'rxjs';
import {
    ForgotPasswordEncryptionConfig,
    ForgotPasswordEncryptionService,
} from '../services/forgot-password-encryption.service';

interface ForgotPasswordAPIParams {
    Username: string;
}

@Injectable()
export class LibAuthForgotPasswordDL extends ApiServiceBase {
    private _apiParams!: ForgotPasswordAPIParams;
    private forgotPasswordEncryptionService = inject(
        ForgotPasswordEncryptionService
    );
    private readonly defaultAppConfig = inject(DEFAULT_APP_CONFIG_TOKEN);

    constructor() {
        super('forgot_password_data', 'FORGOT_PASSWORD');
        this.init();
    }

    buildApiParams(data: ForgotPasswordAPIParams) {
        this._apiParams = data;
    }

    load(): Observable<any> {
        const url = `${this.getApiUrl()}`;
        
        const encryptionConfig: ForgotPasswordEncryptionConfig = {
            siteId: this.defaultAppConfig['siteId'] || 1010,
            applicationName: 'OnlineWaiver',
            applicationVersion: '1.0.0',
            applicationIdentifier: 'waiver-app',
            loginId: this._apiParams.Username,
        };

        return this.forgotPasswordEncryptionService
            .encrypt(encryptionConfig)
            .pipe(
                switchMap((encryptedPayload) =>
                    this._http.post(url, encryptedPayload)
                )
            );
    }
}
