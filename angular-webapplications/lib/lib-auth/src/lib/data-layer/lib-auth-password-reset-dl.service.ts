/**
 * @fileoverview Data layer service for handling password reset API calls
 * <AUTHOR>
 * @version 1.0.0
 */

import { inject, Injectable } from '@angular/core';
import { ApiServiceBase, DEFAULT_APP_CONFIG_TOKEN } from 'lib-app-core';
import { Observable, switchMap } from 'rxjs';
import { ResetPasswordParams } from '../interfaces/forgot-password.interface';
import { PasswordResetEncryptionConfig, PasswordResetEncryptionService } from '../services/password-reset-encryption.service';

@Injectable()
export class LibAuthPasswordResetDL extends ApiServiceBase {
    private _apiParams!: ResetPasswordParams;
    private readonly passwordResetEncryptionService = inject(PasswordResetEncryptionService);
    private readonly defaultAppConfig = inject(DEFAULT_APP_CONFIG_TOKEN);


    constructor() {
        super('password_reset_data', 'PASSWORD_RESET');
        this.init();
    }

    buildApiParams(data: ResetPasswordParams) {
        this._apiParams = data;
    }

    //Returns the API URL for the password reset request
    //This method is used to construct the URL for the API call
    load(): Observable<any> {
        const url = `${this.getApiUrl()}`;
        
          // Create encryption config using the PasswordResetEncryptionConfig interface
          const encryptionConfig: PasswordResetEncryptionConfig = {
            siteId: this.defaultAppConfig['siteId'] || 1010,
            applicationName: 'WaiverWebsite',
            applicationVersion: '1.0.0',
            applicationIdentifier: 'WaiverWebsite',
            loginId: this._apiParams.Email,
            newPassword: this._apiParams.Password,
        };
        return this.passwordResetEncryptionService
        .encrypt(encryptionConfig)
        .pipe(
            switchMap((encryptedPayload) =>
                this._http.post(url, encryptedPayload)
            )
        );
    }
}
