/**
 * @fileoverview Data layer service for handling security token API calls
 * <AUTHOR>
 * @version 1.0.0
 */

import { Injectable } from '@angular/core';
import { ApiServiceBase } from 'lib-app-core';
import { Observable, tap } from 'rxjs';
import {
    ValidateTokenParams,
    ValidateTokenResponse,
} from '../interfaces/reset-password.interface';

@Injectable()
export class LibAuthSecurityTokenDL extends ApiServiceBase {
    private _apiParams!: ValidateTokenParams;

    constructor() {
        super('validate_token', 'VALIDATE_TOKEN');
        this.init();
    }

    //Builds the API parameters for the validate token request
    //This method is called by the business layer to set the parameters before making the API call
    buildApiParams(data: ValidateTokenParams) {
        this._apiParams = data;
    }

    //Returns the API URL for the validate token request
    //This method is used to construct the URL for the API call
    load(): Observable<ValidateTokenResponse> {
        const payload = this._apiParams;
        const url = this.getApiUrl();

        return this._http.post<ValidateTokenResponse>(url, payload);
    }
}
