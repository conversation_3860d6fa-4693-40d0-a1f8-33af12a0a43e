import { CommonModule } from '@angular/common';
import { Component, computed, input, output } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { RouterLink } from '@angular/router';
import { areEqual } from 'lib-app-core';
import { CheckBoxComponent, DynamicFormField, ErrorMessage, FieldDescription, PageFooterComponent, TextInputComponent, SkeletonLoaderComponent } from 'lib-ui-kit';
import { DynamicFormComponent } from 'projects/online-waiver/src/app/components/dynamic-form/dynamic-form.component';
import { TermsAndConditionsComponent } from 'projects/online-waiver/src/app/components/terms-and-conditions/terms-and-conditions.component';
import { waiverConstants } from 'projects/online-waiver/src/app/constants/waiver.constant';

interface RegisterFormData {
    password: FormControl<string | null>;
    confirmPassword: FormControl<string | null>;
}

export const MINIMUM_USER_AGE = 18;

export const STRONG_PASSWORD_REGEX =
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
@Component({
    selector: 'lib-registration-form-base',
    imports: [
        CommonModule,
        ReactiveFormsModule,
        PageFooterComponent,
        DynamicFormComponent,
        TextInputComponent,
        RouterLink,
        TermsAndConditionsComponent,
        SkeletonLoaderComponent,
        CheckBoxComponent
    ],
    templateUrl: './registration-form-base.component.html',
    styleUrl: './registration-form-base.component.css',
})
export class RegistrationFormBaseComponent {
    registerForm = input.required<FormGroup>();
    fieldDescription = input<FieldDescription<RegisterFormData>>();
    errorMessages = input<ErrorMessage<RegisterFormData>>();
    fields = input.required<DynamicFormField[]>();
    isFieldsLoading = input<boolean>(false);
    isSubmitting = input<boolean>(false);
    submit = output<FormGroup>();
    termsAccepted = output<boolean>();

    termsAndConditionsField = computed(() => {
        return this.fields()?.find((field) =>
            areEqual(field.fieldName, waiverConstants.TERMS_AND_CONDITIONS)
        );
    });

    optInPromotionsField = computed(() => {
        return this.fields()?.find((field) =>
            areEqual(field.fieldName, waiverConstants.OPT_IN_PROMOTIONS)
        );
    });
}
