@if (profileData$ | async; as profileData) {
<div class="w-full max-w-[32rem] mx-auto p-6 bg-white rounded-2xl shadow flex flex-col gap-6">
    <p i18n="reset-password.create-new-password" class="text-base font-semibold text-zinc-950">Create a new password</p>
    <p i18n="reset-password.your-new-password-must-be-different-from-the-previous-password"
        class="text-sm font-normal text-neutral-500">Your new password must be different from the previous password.</p>

    <form [formGroup]="form" (ngSubmit)="onSubmit(profileData)" class="flex flex-col gap-4">
        <lib-text-input formControlName="password" label="New Password" type="password" placeholder="Enter new password"
            [required]="true" [showPasswordToggle]="true" [description]="fieldDescription.password"
            [errorMessages]="errorMessages.password" />

        <lib-text-input formControlName="confirmPassword" label="Confirm New Password" type="password"
            placeholder="Re-enter password" [required]="true" [showPasswordToggle]="true"
            [errorMessages]="errorMessages.confirmPassword" />

        <button [disabled]="form.invalid || loading()"
            class="w-full py-3 mt-2 text-base font-medium text-white bg-black-900 rounded-full transition disabled:opacity-60">
            <span i18n="reset-password.confirm-password"> {{ loading() ? 'Resetting Password...' : 'Confirm Password' }}</span>
        </button>
    </form>

    <div class="text-center text-sm mt-2">
        <p i18n="reset-password.remembered-it">Remembered it?</p>
        <a i18n="reset-password.login" routerLink="/auth/login" class="text-blue-600 underline">Login</a>
    </div>
</div>
} @else {
<lib-skeleton-loader [count]="4"
    wrapperClass="w-full max-w-[32rem] mx-auto p-6 rounded-2xl shadow flex flex-col items-center justify-center gap-6"
    skeletonClass="w-full h-12 bg-gray-300 animate-pulse rounded">
</lib-skeleton-loader>
}