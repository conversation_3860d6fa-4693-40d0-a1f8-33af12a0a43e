@let status = status$ | async;
<div class="min-h-screen flex items-center justify-center p-4 bg-gray-50">
    <div class="w-full max-w-md p-8 text-center bg-white rounded-lg border border-blue-200 shadow-sm">
        <!-- Icon -->
        @if (status === 'success') {
        <div class="flex justify-center mb-6">
            <img src="assets/icons/success.svg" alt="Success" class="w-24 h-24 object-contain" />
        </div>
        <h1 i18n="status-result.success" class="text-2xl font-bold mb-4 text-green-600">Success</h1>
        } @else {
        <div class="flex justify-center mb-6">
            <img src="assets/icons/failure.svg" alt="Failure" class="w-24 h-24 object-contain" />
        </div>
        <h1 i18n="status-result.failed" class="text-2xl font-bold mb-4 text-red-600">Failed</h1>
        }
        <!-- Message from query params -->
        @if (queryParams$ | async; as queryParams) {
        <p i18n="status-result.message" class="text-base mb-8 text-gray-800">
            {{ queryParams.message }}
        </p>

        <!-- Button -->
        @if (queryParams.redirectLabel) {
        <lib-button type="primary" size="lg" [fullWidth]="true" (clicked)="navigateToUrl(queryParams.redirectUrl)">
            <span i18n="status-result.redirect-label"> {{ queryParams.redirectLabel }}</span>
        </lib-button>
        } }
    </div>
</div>