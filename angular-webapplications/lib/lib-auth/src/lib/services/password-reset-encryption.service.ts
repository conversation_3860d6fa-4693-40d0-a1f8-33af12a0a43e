/**
 * @fileoverview Service for encrypting password reset requests
 * <AUTHOR>
 * @version 1.0.0
 */

import { Injectable } from '@angular/core';
import { map, Observable } from 'rxjs';
import { EncryptionBaseService } from './encryption-service-base.service';
import CryptoES from 'crypto-es';

/**
 * Configuration interface for password reset encryption process
 * Contains all necessary parameters for encrypting password reset data
 */
export interface PasswordResetEncryptionConfig {
    siteId: number;
    applicationName: string;
    applicationVersion: string;
    applicationIdentifier: string;
    loginId: string;
    newPassword: string;
}

/**
 * Interface for the encrypted password reset request payload
 * Contains all encrypted data and metadata for password reset
 */
export interface EncryptedPasswordResetRequest {
    EncryptedUserName: string;
    EncryptedNewPassword: string;
    Application: string;
    Version: string;
    Identifier: string;
    SiteId: number;
    EncryptedKeyMaterial: string;
}

@Injectable()
export class PasswordResetEncryptionService extends EncryptionBaseService {
    /**
     * Encrypts password reset data using hybrid encryption (AES + RSA)
     *
     * Process:
     * 1. Initializes encryption setup (RSA public key, server time)
     * 2. Generates AES key for symmetric encryption
     * 3. Encrypts sensitive data (username, new password) with AES
     * 4. Encrypts AES key material (key + timestamp) with RSA
     * 5. Returns complete encrypted payload
     *
     * @param config - Password reset encryption configuration containing credentials and metadata
     * @returns Observable of encrypted password reset request payload
     */
    override encrypt(
        config: PasswordResetEncryptionConfig
    ): Observable<EncryptedPasswordResetRequest> {
        this.buildEncryptionParams(config);

        return this.initializeEncryption().pipe(
            map(() => {
                if (!this.aesKey) this.generateAESKey(256);
                const base64AesKey = this.aesKey!.toString(CryptoES.enc.Base64);
                const keyMaterial = `${base64AesKey}|${this.secondsSinceEpoch}`;
                const encryptedKeyMaterial = this.rsaEncrypt(keyMaterial);

                const payload: EncryptedPasswordResetRequest = {
                    EncryptedUserName: this.aesEncrypt(config.loginId),
                    EncryptedNewPassword: this.aesEncrypt(config.newPassword),
                    Application: config.applicationName,
                    Version: config.applicationVersion,
                    Identifier: config.applicationIdentifier,
                    SiteId: config.siteId,
                    EncryptedKeyMaterial: encryptedKeyMaterial,
                };

                return payload;
            })
        );
    }
}