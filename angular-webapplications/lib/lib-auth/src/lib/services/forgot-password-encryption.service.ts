/**
 * @fileoverview Service for encrypting login requests
 * <AUTHOR>
 * @version 1.0.0
 */

import { Injectable } from '@angular/core';
import { map, Observable } from 'rxjs';
import { EncryptionBaseService } from './encryption-service-base.service';
import CryptoES from 'crypto-es';

/**
 * Configuration interface for login encryption process
 * Contains all necessary parameters for encrypting login credentials
 */
export interface ForgotPasswordEncryptionConfig {
    siteId: number;
    applicationName: string;
    applicationVersion: string;
    applicationIdentifier: string;
    loginId: string;
}

/**
 * Interface for the encrypted login request payload
 * Contains all encrypted data and metadata for authentication
 */
export interface EncryptedForgotPasswordRequest {
    EncryptedUserName: string;
    Application: string;
    Version: string;
    Identifier: string;
    SiteId: number;
    EncryptedKeyMaterial: string;
}

@Injectable()
export class ForgotPasswordEncryptionService extends EncryptionBaseService {
    /**
     * Encrypts login credentials using hybrid encryption (AES + RSA)
     *
     * Process:
     * 1. Initializes encryption setup (RSA public key, server time)
     * 2. Generates AES key for symmetric encryption
     * 3. Encrypts sensitive data (username, password, machine name) with AES
     * 4. Encrypts AES key material (key + timestamp) with RSA
     * 5. Returns complete encrypted payload
     *
     * @param config - Login encryption configuration containing credentials and metadata
     * @returns Observable of encrypted login request payload
     */
    override encrypt(
        config: ForgotPasswordEncryptionConfig
    ): Observable<EncryptedForgotPasswordRequest> {
        this.buildEncryptionParams(config);

        return this.initializeEncryption().pipe(
            map(() => {
                if (!this.aesKey) this.generateAESKey(256);
                const base64AesKey = this.aesKey!.toString(CryptoES.enc.Base64);
                const keyMaterial = `${base64AesKey}|${this.secondsSinceEpoch}`;
                const encryptedKeyMaterial = this.rsaEncrypt(keyMaterial);

                const payload: EncryptedForgotPasswordRequest = {
                    EncryptedUserName: this.aesEncrypt(config.loginId),
                    Application: config.applicationName,
                    Version: config.applicationVersion,
                    Identifier: config.applicationIdentifier,
                    SiteId: config.siteId,
                    EncryptedKeyMaterial: encryptedKeyMaterial,
                };

                return payload;
            })
        );
    }
}
