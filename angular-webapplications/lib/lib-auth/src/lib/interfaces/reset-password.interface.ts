/**
 * @fileoverview Interfaces for reset password functionality
 * <AUTHOR>
 * @version 1.0.0
 */
export interface ResetPasswordFormData {
    password: string;
    confirmPassword: string;
}

export interface ResetPasswordQueryParams {
    resetToken?: string;
}

export interface TokenValidationResponse {
    data: {
        ObjectGuid: string;
    };
}
export interface ValidateTokenParams {
    Token: string;
}
export interface ValidateTokenResponse {
    data: {
        TokenId: number;
        Token: string;
        TableObject: string;
        ObjectGuid: string;
        StartTime: string;
        ExpiryTime: string;
        LastActivityTime: string;
        InvalidAttempts: number;
        IsActive: string;
        CreatedBy: string;
        CreationDate: string;
        LastUpdatedBy: string;
        LastUpdatedDate: string;
        SiteId: number;
        MasterEntityId: number;
        SynchStatus: boolean;
        Guid: string;
        TokenValidated: boolean;
        TokenValidationMessage: string | null;
        LoginId: string;
        LanguageId: number;
        RoleId: number;
        MachineId: number;
        UserSessionId: string;
        IsChanged: boolean;
    };
}