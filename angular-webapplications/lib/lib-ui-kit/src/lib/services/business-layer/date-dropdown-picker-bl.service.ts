/**
 * @fileoverview DateDropdownPickerBLService is a service that provides business logic for the DateDropdownPickerComponent
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-07-30
 */
import { Injectable } from '@angular/core';
import { getDaysInMonth } from 'date-fns';
import { DropdownOption } from 'lib-ui-kit';    

@Injectable()
export class DateDropdownPickerBLService {
    private static readonly DEFAULT_DAYS = 31;
    private static readonly MONTHS_IN_YEAR = 12;

    /**
     * Array of months with display names and values
     * Used for populating the month dropdown
     */
    readonly months: readonly DropdownOption[] = [
        { label: 'Jan', value: 1 },
        { label: 'Feb', value: 2 },
        { label: 'Mar', value: 3 },
        { label: 'Apr', value: 4 },
        { label: 'May', value: 5 },
        { label: 'Jun', value: 6 },
        { label: 'Jul', value: 7 },
        { label: 'Aug', value: 8 },
        { label: 'Sep', value: 9 },
        { label: 'Oct', value: 10 },
        { label: 'Nov', value: 11 },
        { label: 'Dec', value: 12 },
    ] as const;

    /**
     * Gets day options for dropdown
     * @param month - Month number (1-12), 0 if not selected
     * @param year - Year number, 0 if not selected
     * @returns Array of day options
     */
    getDayOptions(month: number, year: number): readonly DropdownOption[] {
        const options: DropdownOption[] = [];
        
        // If month is valid, return correct days for that month
        if (this.isValidMonth(month)) {
            // Use provided year or current year if year is not provided
            const yearToUse = year > 0 ? year : new Date().getFullYear();
            
            // getDaysInMonth expects month to be 0-indexed, but our months are 1-indexed
            const daysInMonth = getDaysInMonth(new Date(yearToUse, month - 1, 1));
            for (let i = 1; i <= daysInMonth; i++) {
                options.push({ value: i, label: i.toString() });
            }
        } else {
            // If month is not selected, return 31 days as default
            for (let i = 1; i <= DateDropdownPickerBLService.DEFAULT_DAYS; i++) {
                options.push({ value: i, label: i.toString() });
            }
        }
        
        return options;
    }

    /**
     * Gets month options for dropdown
     * @returns Array of month options
     */
    getMonthOptions(): readonly DropdownOption[] {
        return this.months;
    }

    /**
     * Gets year options for dropdown (4-digit)
     * @param minYear - Minimum year to include
     * @param maxYear - Maximum year to include
     * @returns Array of year options
     */
    getYearOptions(minYear: number, maxYear: number): readonly DropdownOption[] {
        const options: DropdownOption[] = [];
        
        for (let year = maxYear; year >= minYear; year--) {
            options.push({ value: year, label: year.toString() });
        }
        
        return options;
    }

    /**
     * Checks if a year is a leap year
     * @param year - Year to check
     * @returns True if the year is a leap year
     */
    isLeapYear(year: number): boolean {
        return year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0);
    }

    /**
     * Validates if a month number is valid (1-12)
     * @param month - Month number to validate
     * @returns True if the month is valid
     */
    private isValidMonth(month: number): boolean {
        return month > 0 && month <= DateDropdownPickerBLService.MONTHS_IN_YEAR;
    }

    /**
     * Gets the number of days in a specific month and year
     * @param month - Month number (1-12)
     * @param year - Year number
     * @returns Number of days in the month
     */
    getDaysInMonth(month: number, year: number): number {
        if (!this.isValidMonth(month)) {
            return DateDropdownPickerBLService.DEFAULT_DAYS;
        }
        
        return getDaysInMonth(new Date(year, month - 1, 1));
    }

    /**
     * Validates if a date is valid
     * @param day - Day number
     * @param month - Month number (1-12)
     * @param year - Year number
     * @returns True if the date is valid
     */
    isValidDate(day: number, month: number, year: number): boolean {
        if (!this.isValidMonth(month) || day <= 0 || year <= 0) {
            return false;
        }
        
        const daysInMonth = this.getDaysInMonth(month, year);
        return day <= daysInMonth;
    }
}
