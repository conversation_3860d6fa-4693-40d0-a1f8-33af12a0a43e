/**
 * @fileoverview Click outside directive to detect clicks outside of an element
 * <AUTHOR>
 * @created 2025-08-25
 */
import { isPlatformServer } from '@angular/common';
import { Directive, ElementRef, HostListener, inject, output, PLATFORM_ID } from '@angular/core';

@Directive({
    selector: '[libClickOutside]',
})
export class ClickOutsideDirective {
    clickOutside = output<void>();
    private readonly platformId = inject(PLATFORM_ID);
    private readonly isServer = isPlatformServer(this.platformId);
    private startX = 0;
    private startY = 0;
    private moved = false;
    private activePointerId: number | null = null;
    private readonly MOVE_THRESHOLD = 10; // px

    constructor(private el: ElementRef<HTMLElement>) {}

    @HostListener('document:pointerdown', ['$event'])

    onPointerDown(ev: PointerEvent) {
        if (this.isServer) return;
        this.activePointerId = ev.pointerId;
        this.startX = ev.clientX;
        this.startY = ev.clientY;
        this.moved = false;
    }

    @HostListener('document:pointermove', ['$event'])
    onPointerMove(ev: PointerEvent) {
        if (this.isServer || this.activePointerId !== ev.pointerId) return;
        if (
            Math.abs(ev.clientX - this.startX) > this.MOVE_THRESHOLD ||
            Math.abs(ev.clientY - this.startY) > this.MOVE_THRESHOLD
        ) {
            this.moved = true; // scrolling/dragging -> not a tap
        }
    }

    @HostListener('document:pointerup', ['$event'])
    onPointerUp(ev: PointerEvent) {
        if (this.isServer || this.activePointerId !== ev.pointerId) return;
        this.activePointerId = null;

        // Ignore if it was a scroll/drag
        if (this.moved) return;

        const host = this.el.nativeElement;

        // Use composedPath to reliably detect ancestry across shadow DOM
        const path = (ev.composedPath?.() ?? []) as unknown[];
        const clickedInside =
            path.includes(host) || host.contains(ev.target as Node);

        if (!clickedInside) {
            this.clickOutside.emit();
        }
    }

    @HostListener('document:pointercancel')
    onPointerCancel() {
        if (this.isServer) return;
        // Browser took over the gesture (e.g., scroll)
        this.activePointerId = null;
        this.moved = true;
    }
}
