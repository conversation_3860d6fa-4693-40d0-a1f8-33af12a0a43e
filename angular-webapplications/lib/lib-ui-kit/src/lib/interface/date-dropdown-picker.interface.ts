/**
 * @fileoverview DateDropdownPickerComponent interface
 *
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-07-30
 */

import { DropdownOption } from "lib-ui-kit";

/**
 * Available date formats for the dropdown picker
 */
export type DateFormat =
    | 'dd-MM-yyyy'
    | 'MM-dd-yyyy'
    | 'yyyy-MM-dd'
    | 'dd-yyyy-MM'
    | 'MM-yyyy-dd'
    | 'yyyy-dd-MM';

/**
 * Interface representing a date value with day, month, and year components
 */
export interface DateDropdownValue {
    day: number | null;
    month: number | null;
    year: number | null;
}

/**
 * Interface for dropdown field configuration
 */
export interface DateFieldConfig {
    type: 'day' | 'month' | 'year';
    placeholder: string;
    options: readonly DropdownOption[];
}

