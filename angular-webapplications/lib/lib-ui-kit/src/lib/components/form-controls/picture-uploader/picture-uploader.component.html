<div>
    @if (label()) {
    <label i18n="picture-uploader.label" class="block text-xs font-medium text-primary mb-2" [for]="id()">
        {{ label() }}
        @if (isRequired) {
        <span class="text-red-500 font-bold">*</span>
        }
    </label>
    }

    <div class="relative w-full">
        @if (previewUrl()) {
        <!-- Preview Mode -->
        <div class="relative w-full">
            <div class="flex items-center w-full py-3.5 px-4 bg-surface-white rounded-xl border border-surface">
                <div class="rounded overflow-hidden mr-3 flex-shrink-0">
                    <img [src]="previewUrl()" alt="Profile picture preview"
                        class="w-5 h-5 object-cover object-center" />
                </div>
                <p i18n="picture-uploader.file-name" class="text-sm text-neutral-dark flex-grow truncate">
                    {{ getFile()?.name || 'image.jpg' }}
                </p>
                <button type="button" (click)="removePicture()" (keyup.enter)="removePicture()" class="flex-shrink-0"
                    aria-label="Remove profile picture">
                    <img src="assets/icons/close-red.svg" class="w-5 h-5" alt="Remove profile picture">
                </button>
            </div>
        </div>
        } @else {
        <!-- Upload Mode -->
        <div class="w-full">
            <div class="relative w-full py-3.5 px-4 bg-surface-white rounded-xl border border-surface flex items-center transition-all focus-within:ring-1 focus-within:ring-secondary-blue"
                (dragover)="onDragOver($event)" (dragleave)="onDragLeave($event)" (drop)="onDrop($event)" tabindex="0"
                role="button" aria-label="Upload profile picture" [attr.aria-busy]="isLoading()"
                [attr.aria-invalid]="hasError()" [class.ring-1]="hasError()" [class.ring-feedback-error]="hasError()">
                <div class="flex items-center w-full">
                    @if (isLoading()) {
                    <div role="status" class="flex items-center w-full">
                        <div class="w-5 h-5 border-2 border-t-transparent rounded-full animate-spin mr-3">
                        </div>
                        <p i18n="picture-uploader.processing" class="text-sm text-neutral-dark">Processing...</p>
                        <p i18n="picture-uploader.uploading-profile-picture" class="sr-only">Uploading profile picture</p>
                    </div>
                    } @else {
                    <img src="assets/icons/user.svg" class="w-5 h-5 mr-3 flex-shrink-0" alt="Upload profile picture">
                    <p i18n="picture-uploader.upload-profile-picture" class="text-sm text-neutral-dark">
                        {{ placeholder() }}
                    </p>
                    }
                </div>

                <input type="file" [name]="name()" [id]="id()" [accept]="accept()" (change)="onFileSelected($event)"
                    (blur)="onBlur()" class="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    [disabled]="disabled() || isLoading()" />
            </div>
        </div>
        }
    </div>

    <!-- Error & Description Handling -->
    @if (hasError()) {
    <div class="text-feedback-error text-xs mt-2">
        @if (isTemplateRef(errorMessage)) {
        <ng-container [ngTemplateOutlet]="$any(errorMessage)"></ng-container>
        } @else {
        <p i18n="picture-uploader.error-message">{{ errorMessage }}</p>
        }
    </div>
    } @else if (description()) {
    <p i18n="picture-uploader.description" class="text-neutral-dark text-xs mt-2">
        {{ description() }}
    </p>
    }
</div>