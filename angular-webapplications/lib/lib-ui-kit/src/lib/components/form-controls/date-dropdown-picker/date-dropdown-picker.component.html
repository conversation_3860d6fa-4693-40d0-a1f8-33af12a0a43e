<div class="flex flex-col w-full">
    <label i18n="date-dropdown-picker.label" class="block text-xs text-primary mb-2" [for]="id()">
        {{ label() }}
        @if (required()) {
        <span class="text-red-500 font-bold">*</span>
        }
    </label>
    
    <div class="flex gap-1 lg:gap-2 items-center justify-between w-full" [id]="id()">
        @for (field of fieldConfig(); track field.type) {
            <lib-select 
                [id]="id() + '-' + field.type"
                [options]="field.options"
                class="w-full min-w-[60px]"
                [placeholder]="field.placeholder"
                [customClass]="customClasses"
                [ngModel]="getFieldModelValue(field.type)"
                (ngModelChange)="setFieldModelValue($event, field.type)"
                [disabled]="disabled()">
            </lib-select>
        }
    </div>

    <!-- Error message -->
    @if (hasError()) {
        <div class="text-feedback-error text-xs mt-2">
            @if (errorMessage && isTemplateRef(errorMessage)) {
                <ng-container *ngTemplateOutlet="$any(errorMessage)"></ng-container>
            } @else {
                <p i18n="date-dropdown-picker.error-message">{{ errorMessage }}</p>
            }
        </div>
    }
</div>