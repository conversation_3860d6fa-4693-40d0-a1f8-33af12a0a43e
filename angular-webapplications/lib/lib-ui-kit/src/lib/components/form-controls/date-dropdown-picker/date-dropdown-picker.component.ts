/**
 * @fileoverview DateDropdownPickerComponent is a component that allows the user to select a date from a dropdown menu
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-07-30
 */
import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, forwardRef, inject, Injector, input, signal, TemplateRef } from '@angular/core';
import {
    ControlValueAccessor,
    FormsModule,
    NG_VALUE_ACCESSOR,
    NgControl,
    Validators
} from '@angular/forms';
import { DATE_FORMAT, DATE_VALUE_FORMAT, SelectComponent, YEARS_OFFSET } from 'lib-ui-kit';
import {
    DateFieldConfig,
    DateFormat
} from '../../../interface/date-dropdown-picker.interface';
import { DateDropdownPickerBLService } from '../../../services/business-layer/date-dropdown-picker-bl.service';
import { isTemplateRef } from '../../../utils';
import { format } from 'date-fns';

@Component({
    selector: 'lib-date-dropdown-picker',
    standalone: true,
    imports: [CommonModule, SelectComponent, FormsModule],
    templateUrl: './date-dropdown-picker.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => DateDropdownPickerComponent),
            multi: true,
        },
        DateDropdownPickerBLService,
    ],
})
export class DateDropdownPickerComponent implements ControlValueAccessor {
    // Input properties
    id = input<string>('');
    label = input<string>('');
    name = input<string>('');
    placeholder = input<string>('');
    required = input<boolean>(false);
    disabled = input<boolean>(false);
    format = input<DateFormat>(DATE_FORMAT.DD_MM_YYYY);
    errorMessages = input<Record<string, string | TemplateRef<Component> | undefined>>();
    minYear = input<number | undefined>();
    maxYear = input<number | undefined>();
    customClass = input<string>('');
    defaultMinYear = new Date().getFullYear() - YEARS_OFFSET;
    defaultMaxYear = new Date().getFullYear() + YEARS_OFFSET;

    // Internal state signals
    private readonly _value = signal<string | null>(null);
    private readonly _isDisabled = signal<boolean>(false);
    private readonly _dayValue = signal<string | null>(null);
    private readonly _monthValue = signal<string | null>(null);
    private readonly _yearValue = signal<string | null>(null);

    // Computed values
    readonly dayOptions = computed(() => 
        this.blService.getDayOptions(Number(this._monthValue()), Number(this._yearValue()))
    );
    
    readonly monthOptions = computed(() => 
        this.blService.getMonthOptions()
    );
    
    readonly yearOptions = computed(() => 
        this.blService.getYearOptions(this.minYear() || this.defaultMinYear, this.maxYear() || this.defaultMaxYear)
    );

    readonly fieldConfig = computed((): DateFieldConfig[] => {
        const configs: DateFieldConfig[] = [];
        const parts = this.format().split('-');
        
        parts.forEach((part) => {
            switch (part.toLowerCase()) {
                case 'dd':
                    configs.push({
                        type: 'day',
                        placeholder: 'Day',
                        options: this.dayOptions(),
                    });
                    break;
                case 'mm':
                    configs.push({
                        type: 'month',
                        placeholder: 'Month',
                        options: this.monthOptions(),
                    });
                    break;
                case 'yyyy':
                    configs.push({
                        type: 'year',
                        placeholder: 'Year',
                        options: this.yearOptions(),
                    });
                    break;
            }
        });
    
        return configs;
    });
    
    readonly currentValue = computed((): string | null => {
        const day = Number(this._dayValue());
        const month = Number(this._monthValue());
        const year = Number(this._yearValue());
        
        if (day && month && year) {
            return format(new Date(year, month, day), DATE_VALUE_FORMAT);
        }

        return null;
    });

    readonly isRequired = computed((): boolean => {
        return (
            this.required() ||
            !!this.ngControl?.control?.hasValidator?.(Validators.required)
        );
    });

    hasError() {
        return (
            !!this.ngControl?.control?.invalid &&
            !!this.ngControl?.control?.touched
        );
    }

    get errorMessage() {
        const errors = this.ngControl?.control?.errors;
        if (!errors) return null;

        const errorKeys = Object.keys(errors);
        if (errorKeys.length > 0) {
            const firstErrorKey = errorKeys[0];
            if (firstErrorKey === 'invalidDate') {
                return 'Invalid date';
            }
            return this.errorMessages()?.[firstErrorKey];
        }
        return null;
    }

    get customClasses() {
        let className = '';

        if (this.hasError()) {
            className = 'border-none ring-1 ring-feedback-error';
        } else if (this._isDisabled()) {
            className = 'cursor-not-allowed';
        }
        
        if (this.customClass()) {
            className = `${className} ${this.customClass()}`;
        }
        
        return className;
    }

    // Utility function
    readonly isTemplateRef = isTemplateRef;

    // Dependencies
    private readonly blService = inject(DateDropdownPickerBLService);
    private readonly injector = inject(Injector);
    private ngControl: NgControl | null = null;
    private onChange: (value: string | null) => void = () => {};
    private onTouched: () => void = () => { };

    ngOnInit(): void {
        const ngControl = this.injector.get(NgControl, null);
        if (ngControl) {
            this.ngControl = ngControl;
        }
    }

    // ControlValueAccessor implementation
    writeValue(value: string | null): void {
        this._value.set(value);
        this.parseValueToFields(value);
    }

    registerOnChange(fn: (value: string | null) => void): void {
        this.onChange = fn;
    }

    registerOnTouched(fn: () => void): void {
        this.onTouched = fn;
    }

    setDisabledState(isDisabled: boolean): void {
        this._isDisabled.set(isDisabled);
    }

    // Public methods
    getFieldModelValue(type: 'day' | 'month' | 'year'): string | null {
        switch (type) {
            case 'day':
                return this._dayValue();
            case 'month':
                return this._monthValue();
            case 'year':
                return this._yearValue();
            default:
                return null;
        }
    }

    setFieldModelValue(value: string, type: 'day' | 'month' | 'year'): void {
        switch (type) {
            case 'day':
                this._dayValue.set(value);
                break;
            case 'month':
                this._monthValue.set(value);
                this.handleMonthChange(Number(value));
                break;
            case 'year':
                this._yearValue.set(value);
                this.handleYearChange(Number(value));
                break;
        }
        
        this.onChange(this.currentValue());
        this.onTouched();
    }

    // Private helper methods
    private parseValueToFields(value: string | null): void {
        if (!value) {
            this._dayValue.set(null);
            this._monthValue.set(null);
            this._yearValue.set(null);
            return;
        }

        const parts = value.split('-');
        if (parts.length === 3) {
            const [year, month, day] = parts;
            this._yearValue.set(year);
            this._monthValue.set(month);
            this._dayValue.set(day);
        }
    }

    private handleMonthChange(newMonth: number): void {
        const currentDay = Number(this._dayValue());
        const year = Number(this._yearValue()) || new Date().getFullYear();
        
        if (currentDay > 0) {
            const daysInNewMonth = this.blService.getDaysInMonth(newMonth, year);
            if (currentDay > daysInNewMonth) {
                this._dayValue.set(null);
                this.ngControl?.control?.setErrors({
                    invalidFebruary: true
                });
            }
        }
    }

    private handleYearChange(newYear: number): void {
        const currentDay = Number(this._dayValue());
        const currentMonth = Number(this._monthValue());
        
        if (currentDay === 29 && currentMonth === 2) {
            const isLeapYear = this.blService.isLeapYear(newYear);
            if (!isLeapYear) {
                this._dayValue.set(null);
                this.ngControl?.control?.setErrors({
                    leapYear: true
                });
            }
        }
    }
}
