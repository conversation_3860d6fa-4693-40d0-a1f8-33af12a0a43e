<div class="relative" libClickOutside (clickOutside)="onOutsideClick()">
    <!-- Trigger area -->
    <div (click)="toggle()">
        <ng-content select="[popupTrigger]"></ng-content>
    </div>
    
    <!-- Popup content -->
    @if (isOpen()) {
    <div 
        class="absolute bg-surface-white rounded-4xl border border-surface shadow-sm overflow-hidden popup-enter w-max"
        [style.margin-top]="position() === 'bottom' ? offset() + 'px' : null"
        [style.margin-bottom]="position() === 'top' ? offset() + 'px' : null"
        [style.margin-left]="position() === 'right' ? offset() + 'px' : null"
        [style.margin-right]="position() === 'left' ? offset() + 'px' : null"
        [class.top-full]="position() === 'bottom'"
        [class.bottom-full]="position() === 'top'"
        [class.left-full]="position() === 'right'"
        [class.right-full]="position() === 'left'"
        [class.left-0]="align() === 'start' && (position() === 'top' || position() === 'bottom')"
        [class.md:right-0]="align() === 'end' && (position() === 'top' || position() === 'bottom')"
        [class.left-0.5]="align() === 'center' && (position() === 'top' || position() === 'bottom')"
        [class.transform]="align() === 'center' && (position() === 'top' || position() === 'bottom')"
        [class.-translate-x-0.5]="align() === 'center' && (position() === 'top' || position() === 'bottom')"
        [class.top-0]="align() === 'start' && (position() === 'left' || position() === 'right')"
        [class.bottom-0]="align() === 'end' && (position() === 'left' || position() === 'right')"
        [class.top-0.5]="align() === 'center' && (position() === 'left' || position() === 'right')"
        [class.transform]="align() === 'center' && (position() === 'left' || position() === 'right')"
        [class.-translate-y-0.5]="align() === 'center' && (position() === 'left' || position() === 'right')"
    >
        <ng-content select="[popupContent]"></ng-content>
    </div>
    }
</div>
