import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, input, signal } from '@angular/core';
import { ClickOutsideDirective } from '../../directives/click-outside.directive';

@Component({
    selector: 'lib-popup',
    standalone: true,
    imports: [CommonModule, ClickOutsideDirective],
    templateUrl: './popup.component.html',
    styleUrl: './popup.component.css',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PopupComponent {
    // Input properties
    position = input<'top' | 'bottom' | 'left' | 'right'>('bottom');
    align = input<'start' | 'center' | 'end'>('start');
    offset = input<number>(0);
        
    // Internal state
    readonly isOpen = signal<boolean>(false);

    toggle(): void {
        this.isOpen.set(!this.isOpen());
    }
    
    onOutsideClick(): void {
        this.isOpen.set(false);
    }

    close(): void {
        this.isOpen.set(false);
    }
}
