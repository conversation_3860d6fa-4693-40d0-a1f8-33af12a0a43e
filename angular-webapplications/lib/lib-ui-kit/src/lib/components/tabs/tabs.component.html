<div [class]="getContainerClasses()">
    @for (tab of tabs; track tab.id; let i = $index) {
    @if (getTabState(tab, i); as state) {
    <button class="relative flex flex-1 flex-col py-4 px-6 text-center w-max transition-all duration-150" [ngClass]="{
            'text-gray-400 cursor-not-allowed': state.isDisabled,
            'bg-primary text-white': state.isDefaultActive,
            'bg-gray-100 text-gray-700 hover:bg-gray-200': state.isDefaultInactive,
            'text-primary': state.isRoundedInactive,
            'bg-surface-white text-secondary-blue -mb-[0.25rem] shadow-md': state.isRoundedActive,
            'rounded-tl-[2rem] rounded-tr-[1.5rem] tab-curve-right': state.isFirstActive,
            'rounded-tr-[2rem] rounded-tl-[1.5rem] tab-curve-left': state.isOtherActive
        }" (click)="onTabClick(tab)" [routerLink]="tab.route" routerLinkActive="active" [disabled]="tab.disabled">

        @if (tab.subtitle) {
        <div class="text-xs text-nowrap text-center w-full">
            {{ tab.subtitle }}
        </div>
        }
        <div class="font-semibold text-lg text-center w-full">
            {{ tab.label }}
        </div>
    </button>
    }
    }
</div>