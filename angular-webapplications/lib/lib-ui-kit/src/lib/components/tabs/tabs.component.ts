/**
 * @fileoverview Reusable Tabs component for UI Kit Library
 * @description A generic, reusable tabs component that can be used across any application.
 *              Provides flexible tab functionality with support for different variants,
 *              sizes, and states. This component is domain-agnostic and can be used
 *              for navigation, settings, forms, or any tabbed interface.
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-08-21
 */

import { Component, Input, Output, EventEmitter, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

/**
 * TabItem Interface - Generic Tab Configuration
 *
 * Defines the structure for individual tab items. This interface is designed
 * to be flexible and support various use cases across different applications.
 */
export interface TabItem {
    /** Unique identifier for the tab */
    id: string;
    /** Main text displayed on the tab */
    label: string;
    /** Optional subtitle text for additional context */
    subtitle?: string;
    /** Optional Angular route for navigation */
    route?: string;
    /** Optional icon for visual enhancement (future feature) */
    icon?: string;
    /** Whether the tab is disabled and non-interactive */
    disabled?: boolean;
}

@Component({
    selector: 'lib-tabs',
    standalone: true,
    imports: [CommonModule, RouterModule],
    templateUrl: './tabs.component.html',
    styleUrl: './tabs.component.scss',
})
export class TabsComponent {
    /**
     * Tabs Array - Tab Items Configuration
     *
     * Array of tab items to display. Each item should conform to the TabItem interface.
     * This input allows the parent component to define any number of tabs with
     * custom labels, routes, and states.
     */
    @Input() tabs: TabItem[] = [];

    /**
     * Active Tab ID - Current Active Tab
     *
     * The ID of the currently active tab. If not provided, the first tab
     * will be automatically selected as active.
     */
    @Input() activeTabId: string = '';

    /**
     * Visual Style Variant - Component Appearance
     *
     * Controls the visual style of the tabs:
     * - 'default': Standard rectangular tabs with sharp corners
     * - 'rounded': Rounded tabs with curved corners for modern UI
     */
    @Input() variant: 'default' | 'rounded' = 'default';

    /**
     * Tab Size - Component Dimensions
     *
     * Controls the size of the tabs for different UI contexts:
     * - 'sm': Small tabs for compact interfaces
     * - 'md': Medium tabs for standard interfaces
     * - 'lg': Large tabs for prominent interfaces
     */
    @Input() size: 'sm' | 'md' | 'lg' = 'md';

    /**
     * Tab Change Event - User Interaction Output
     *
     * Emitted when a user clicks on a tab. Provides the ID of the newly
     * selected tab to the parent component for handling navigation or
     * state updates.
     */
    @Output() tabChange = new EventEmitter<string>();

    /**
     * Active Tab Signal - Internal State Management
     *
     * Reactive signal that tracks the currently active tab ID.
     * This provides efficient change detection and state management
     * using Angular's signal system.
     */
    protected readonly activeTab = signal<string>('');

    /**
     * Component Initialization - Active Tab Setup
     *
     * Sets up the initial active tab when the component is initialized.
     * Prioritizes the provided activeTabId, otherwise defaults to the
     * first tab in the array.
     */
    ngOnInit() {
        // Set initial active tab
        if (this.activeTabId) {
            this.activeTab.set(this.activeTabId);
        } else if (this.tabs.length > 0) {
            this.activeTab.set(this.tabs[0].id);
        }
    }

    /**
     * Tab Click Handler - User Interaction Processing
     *
     * Handles user clicks on tab items. Prevents interaction with disabled
     * tabs and updates the active tab state while emitting the change event.
     *
     * @param tab - The tab item that was clicked
     */
    onTabClick(tab: TabItem) {
        // Prevent click if tab is disabled
        if (tab.disabled) return;

        // Update active tab and emit change
        this.activeTab.set(tab.id);
        this.tabChange.emit(tab.id);
    }

    /**
     * Active Tab Checker - State Validation
     *
     * Determines if a specific tab is currently active by comparing
     * its ID with the active tab signal.
     *
     * @param tabId - The ID of the tab to check
     * @returns boolean - True if the tab is active, false otherwise
     */
    isActive(tabId: string): boolean {
        return this.activeTab() === tabId;
    }

    /**
     * Container Classes Generator - Dynamic Styling
     *
     * Generates CSS classes for the tabs container based on the selected variant.
     * Provides consistent styling across different visual themes.
     *
     * @returns string - CSS classes for the container element
     */
    getContainerClasses(): string {
        const baseClasses =
            'relative flex bg-surface-lightest overflow-hidden w-full md:w-max';

        return this.variant === 'rounded'
            ? `${baseClasses} rounded-t-4xl`
            : `${baseClasses} rounded-t-lg`;
    }

    /**
     * Tab State Generator - Comprehensive State Management
     *
     * Generates a comprehensive state object for each tab that includes
     * all necessary information for styling and behavior. This method
     * centralizes state logic and provides a clean interface for the template.
     *
     * @param tab - The tab item to generate state for
     * @param index - The position of the tab in the array
     * @returns object - Complete state object with all tab properties
     */
    getTabState(tab: TabItem, index: number) {
        const isActive = this.isActive(tab.id);
        const isDisabled = tab.disabled || false;
        const isRounded = this.variant === 'rounded';
        const isFirst = index === 0;

        return {
            // Basic states
            isActive,
            isDisabled,
            isRounded,
            isFirst,

            // Variant states
            isDefaultVariant: !isRounded,

            // Rounded variant states
            isRoundedActive: !isDisabled && isActive && isRounded,
            isRoundedInactive: !isDisabled && !isActive && isRounded,

            // Default variant states
            isDefaultActive: !isDisabled && isActive && !isRounded,
            isDefaultInactive: !isDisabled && !isActive && !isRounded,

            // Position states for rounded variant
            isFirstActive: !isDisabled && isActive && isRounded && isFirst,
            isOtherActive: !isDisabled && isActive && isRounded && !isFirst,
        };
    }
}
