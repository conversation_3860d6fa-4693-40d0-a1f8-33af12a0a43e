/**
 * @fileoverview Reusable button component with multiple variants
 * <AUTHOR>
 * @version 1.0.0
 */
import { Component, input, output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

export type ButtonType =
    | 'primary'
    | 'secondary'
    | 'link'
    | 'outline'
    | 'ghost'
    | 'danger';
export type ButtonSize = 'sm' | 'md' | 'lg';

@Component({
    selector: 'lib-button',
    standalone: true,
    imports: [CommonModule],
    templateUrl: './button.component.html',
})
export class ButtonComponent {
    // Inputs
    type = input<ButtonType>('primary');
    size = input<ButtonSize>('md');
    disabled = input<boolean>(false);
    loading = input<boolean>(false);
    fullWidth = input<boolean>(false);
    icon = input<string>('');
    iconPosition = input<'left' | 'right'>('left');

    // Outputs
    clicked = output<void>();

    // Computed classes
    get buttonClasses(): string {
        const baseClasses =
            'inline-flex items-center justify-center font-medium rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';

        const sizeClasses = {
            sm: 'px-3 py-1.5 text-sm',
            md: 'px-4 py-2 text-sm',
            lg: 'px-6 py-3 text-base',
        };

        const typeClasses = {
            primary:
                'bg-primary text-white hover:bg-primary-dark focus:ring-primary',
            secondary:
                'bg-secondary text-white hover:bg-secondary-dark focus:ring-secondary',
            outline:
                'border border-primary text-primary hover:bg-primary hover:text-white focus:ring-primary',
            ghost: 'text-primary hover:bg-primary-light focus:ring-primary',
            link: 'text-primary underline hover:text-primary-dark focus:ring-primary',
            danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',
        };

        const widthClass = this.fullWidth() ? 'w-full' : '';

        return `${baseClasses} ${sizeClasses[this.size()]} ${
            typeClasses[this.type()]
        } ${widthClass}`;
    }

    get iconClasses(): string {
        const sizeClasses = {
            sm: 'w-4 h-4',
            md: 'w-5 h-5',
            lg: 'w-6 h-6',
        };

        return sizeClasses[this.size()];
    }

    onClick(event: Event): void {
        if (!this.disabled() && !this.loading()) {
            this.clicked.emit();
        }
    }
}
