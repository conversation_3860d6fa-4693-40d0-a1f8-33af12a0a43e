<button [class]="buttonClasses" [disabled]="disabled() || loading()" (click)="onClick($event)" type="button">

    <!-- Loading Spinner -->
    @if (loading()) {
    <div class="w-4 h-4 border-2 border-t-transparent rounded-full animate-spin mr-2"></div>
    }

    <!-- Left Icon -->
    @if (icon() && iconPosition() === 'left' && !loading()) {
    <img [src]="icon()" [alt]="'Icon'" [class]="iconClasses + ' mr-2'" />
    }
    <ng-content></ng-content>

    <!-- Right Icon -->
    @if (icon() && iconPosition() === 'right' && !loading()) {
    <img [src]="icon()" [alt]="'Icon'" [class]="iconClasses + ' ml-2'" />
    }
</button>